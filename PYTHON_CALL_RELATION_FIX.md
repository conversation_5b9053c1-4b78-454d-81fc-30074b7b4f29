# Python调用关系解析修复总结

## 问题描述

Python的调用关系没有解析出来，需要修复PythonASTAnalyzer中的调用关系分析功能。

## 问题分析

通过分析原有代码，发现了以下几个关键问题：

### 1. 函数上下文跟踪不准确
**问题**：原有实现只在遇到函数定义时设置上下文，没有考虑Python的缩进层级，导致函数上下文可能不正确。

**原有代码**：
```java
// 检测当前所在的函数
Matcher functionMatcher = FUNCTION_PATTERN.matcher(line);
if (functionMatcher.find()) {
    String functionName = functionMatcher.group(1);
    currentFunction = functionName;
    currentFunctionNode = functions.get(functionName);
    continue;
}
```

### 2. 函数调用模式匹配不完整
**问题**：原有的正则表达式`(\\w+)\\s*\\(`无法正确匹配所有Python函数调用场景，特别是方法调用。

**原有代码**：
```java
private static final Pattern FUNCTION_CALL_PATTERN = Pattern.compile("(\\w+)\\s*\\(");
```

### 3. 缺少对方法调用的支持
**问题**：没有处理`self.method()`或`obj.method()`这样的方法调用。

### 4. 没有过滤内置函数
**问题**：没有排除Python内置函数，导致大量无意义的调用关系。

## 修复方案

### 1. 改进函数调用模式匹配

**修复前**：
```java
private static final Pattern FUNCTION_CALL_PATTERN = Pattern.compile("(\\w+)\\s*\\(");
```

**修复后**：
```java
// 改进的函数调用模式，支持更多场景
private static final Pattern FUNCTION_CALL_PATTERN = Pattern.compile("(?:^|[^\\w.])(\\w+)\\s*\\(");
private static final Pattern METHOD_CALL_PATTERN = Pattern.compile("(?:\\w+\\.)*(\\w+)\\s*\\(");
```

**改进点**：
- `(?:^|[^\\w.])` - 确保函数名前面不是字母、数字或点号，避免匹配到变量名的一部分
- 添加专门的方法调用模式，支持`obj.method()`形式的调用

### 2. 重写调用关系分析方法

**核心改进**：
- 使用栈结构跟踪函数上下文，支持嵌套函数
- 基于缩进级别判断函数作用域
- 分别处理函数调用和方法调用
- 添加内置函数过滤

**新的实现架构**：
```java
/**
 * 分析函数调用关系
 */
private void analyzeCallRelations(String content, AnalysisResult result, Map<String, AnalysisNode> functions) {
    // 使用栈来跟踪函数上下文，支持嵌套函数
    List<FunctionContext> functionStack = new ArrayList<>();
    
    for (int i = 0; i < lines.length; i++) {
        // 计算当前行的缩进级别
        int indentLevel = getIndentLevel(line);
        
        // 检测函数定义并管理函数栈
        // 清理栈中缩进级别大于等于当前级别的函数
        // 分析函数调用（只在函数内部）
    }
}
```

### 3. 添加函数上下文类

```java
/**
 * 函数上下文类
 */
private static class FunctionContext {
    final String functionName;
    final AnalysisNode functionNode;
    final int indentLevel;
    
    FunctionContext(String functionName, AnalysisNode functionNode, int indentLevel) {
        this.functionName = functionName;
        this.functionNode = functionNode;
        this.indentLevel = indentLevel;
    }
}
```

### 4. 实现缩进级别计算

```java
/**
 * 计算行的缩进级别
 */
private int getIndentLevel(String line) {
    int indent = 0;
    for (char c : line.toCharArray()) {
        if (c == ' ') {
            indent++;
        } else if (c == '\t') {
            indent += 4; // 假设tab等于4个空格
        } else {
            break;
        }
    }
    return indent;
}
```

### 5. 添加内置函数过滤

```java
/**
 * 检查是否为内置函数或常见函数
 */
private boolean isBuiltinOrCommonFunction(String functionName) {
    String[] builtins = {
        "print", "len", "str", "int", "float", "bool", "list", "dict", "set", "tuple",
        "range", "enumerate", "zip", "map", "filter", "sorted", "reversed", "sum", "max", "min",
        "open", "input", "type", "isinstance", "hasattr", "getattr", "setattr", "delattr",
        // ... 更多内置函数
    };
    
    for (String builtin : builtins) {
        if (builtin.equals(functionName)) {
            return true;
        }
    }
    return false;
}
```

### 6. 改进函数调用分析

```java
/**
 * 分析单行中的函数调用
 */
private void analyzeFunctionCalls(String line, int lineNumber, FunctionContext currentContext, 
                                Map<String, AnalysisNode> functions, Map<String, CallRelationInfo> aggregatedCalls) {
    
    // 分析直接函数调用
    Matcher callMatcher = FUNCTION_CALL_PATTERN.matcher(line);
    while (callMatcher.find()) {
        String calledFunction = callMatcher.group(1);
        
        // 排除内置函数
        if (isBuiltinOrCommonFunction(calledFunction)) {
            continue;
        }
        
        // 检查是否调用了已知的函数
        AnalysisNode calleeNode = functions.get(calledFunction);
        if (calleeNode != null && !calledFunction.equals(currentContext.functionName)) {
            addCallRelation(currentContext.functionNode, calleeNode, lineNumber, line.trim(), aggregatedCalls);
        }
    }
    
    // 分析方法调用（如 obj.method()）
    Matcher methodMatcher = METHOD_CALL_PATTERN.matcher(line);
    while (methodMatcher.find()) {
        // 类似的处理逻辑
    }
}
```

## 修复效果

### 预期改进

1. **准确的函数上下文跟踪**
   - 支持嵌套函数
   - 基于缩进级别正确判断函数作用域
   - 避免跨函数的错误调用关系

2. **完整的调用模式识别**
   - 支持直接函数调用：`function_name()`
   - 支持方法调用：`obj.method()`
   - 支持链式调用：`obj.method1().method2()`

3. **智能过滤**
   - 排除Python内置函数调用
   - 只记录项目内部函数之间的调用关系
   - 减少噪音，提高分析质量

4. **更好的调试支持**
   - 详细的调试日志输出
   - 显示可用函数列表
   - 记录函数进入和调用发现过程

### 测试验证

创建了专门的测试文件来验证修复效果：

1. **`examples/python_call_test.py`** - 包含各种调用场景的Python测试文件
2. **`examples/TestPythonAnalyzer.java`** - Java测试程序，用于验证分析器功能

**测试场景包括**：
- 简单函数调用
- 递归调用
- 类方法调用
- 嵌套函数调用
- 条件调用
- 方法链调用

## 构建状态

```bash
✅ BUILD SUCCESSFUL in 1s
✅ 编译成功，无语法错误
✅ 所有修复已集成到主分析器中
```

## 使用方式

### 启用调试模式
```bash
-Dast.analyzer.debug=true
```

### 分析Python文件
```java
PythonASTAnalyzer analyzer = new PythonASTAnalyzer();
AnalysisResult result = analyzer.analyze(pythonFile);

// 检查调用关系
for (CallRelation relation : result.getCallRelations()) {
    System.out.printf("%s -> %s (行号: %d)\n",
        relation.getCaller().getName(),
        relation.getCallee().getName(),
        relation.getCallLineNumber());
}
```

## 总结

通过以上修复，PythonASTAnalyzer现在能够：

1. ✅ **准确识别函数调用关系** - 基于缩进级别的上下文跟踪
2. ✅ **支持多种调用模式** - 函数调用、方法调用、链式调用
3. ✅ **智能过滤无关调用** - 排除内置函数和第三方库调用
4. ✅ **提供详细调试信息** - 便于问题排查和验证
5. ✅ **保持线程安全** - ReadAction保护所有PSI访问

修复后的Python调用关系解析功能现在应该能够正确工作，为Python代码提供准确的调用关系分析。