#!/usr/bin/env python3
"""
Sample Python file for testing the enhanced Python AST analyzer.
This file contains various Python constructs to test the analyzer.
"""

import os
import sys
from collections import defaultdict
from typing import List, Dict, Optional

# Module-level variables
DEBUG_MODE = True
CONFIG_PATH = "/etc/config.json"
DEFAULT_TIMEOUT = 30

class DataProcessor:
    """A sample class for data processing."""
    
    def __init__(self, name: str, config: Optional[Dict] = None):
        self.name = name
        self.config = config or {}
        self.processed_count = 0
    
    def process_data(self, data: List[str]) -> Dict[str, int]:
        """Process a list of data items."""
        result = defaultdict(int)
        
        for item in data:
            processed_item = self.clean_data(item)
            if processed_item:
                result[processed_item] += 1
                self.processed_count += 1
        
        return dict(result)
    
    def clean_data(self, item: str) -> Optional[str]:
        """Clean and validate a data item."""
        if not item or not isinstance(item, str):
            return None
        
        cleaned = item.strip().lower()
        return cleaned if len(cleaned) > 0 else None
    
    def get_stats(self) -> Dict[str, int]:
        """Get processing statistics."""
        return {
            "processed_count": self.processed_count,
            "config_items": len(self.config)
        }

class AdvancedProcessor(DataProcessor):
    """An advanced data processor with additional features."""
    
    def __init__(self, name: str, config: Optional[Dict] = None, use_cache: bool = True):
        super().__init__(name, config)
        self.use_cache = use_cache
        self.cache = {}
    
    def process_data(self, data: List[str]) -> Dict[str, int]:
        """Enhanced data processing with caching."""
        if self.use_cache and "data" in self.cache:
            return self.cache["data"]
        
        result = super().process_data(data)
        
        if self.use_cache:
            self.cache["data"] = result
        
        return result
    
    def clear_cache(self):
        """Clear the processing cache."""
        self.cache.clear()

def create_processor(processor_type: str = "basic") -> DataProcessor:
    """Factory function to create data processors."""
    if processor_type == "advanced":
        return AdvancedProcessor("advanced_processor")
    else:
        return DataProcessor("basic_processor")

def load_config(config_path: str = CONFIG_PATH) -> Dict:
    """Load configuration from file."""
    try:
        with open(config_path, 'r') as f:
            import json
            return json.load(f)
    except FileNotFoundError:
        print(f"Config file not found: {config_path}")
        return {}
    except json.JSONDecodeError as e:
        print(f"Invalid JSON in config file: {e}")
        return {}

def process_files(file_paths: List[str], processor: DataProcessor) -> None:
    """Process multiple files using the given processor."""
    for file_path in file_paths:
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                lines = f.readlines()
                result = processor.process_data(lines)
                print(f"Processed {file_path}: {len(result)} unique items")
        else:
            print(f"File not found: {file_path}")

def main():
    """Main function to demonstrate the data processing."""
    config = load_config()
    processor = create_processor("advanced")
    
    sample_data = ["apple", "banana", "apple", "cherry", "banana", "date"]
    result = processor.process_data(sample_data)
    
    print("Processing results:")
    for item, count in result.items():
        print(f"  {item}: {count}")
    
    stats = processor.get_stats()
    print(f"Statistics: {stats}")

if __name__ == "__main__":
    main()
