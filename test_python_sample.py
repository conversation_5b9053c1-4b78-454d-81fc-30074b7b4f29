#!/usr/bin/env python3
"""
Sample Python file for testing the enhanced Python AST analyzer.
This file contains various Python constructs to test the analyzer.
"""

import os
import sys
from collections import defaultdict
from typing import List, Dict, Optional

# Module-level variables
DEBUG_MODE = True
CONFIG_PATH = "/etc/config.json"
DEFAULT_TIMEOUT = 30

class DataProcessor:
    """A sample class for data processing."""
    
    def __init__(self, name: str, config: Optional[Dict] = None):
        self.name = name
        self.config = config or {}
        self.processed_count = 0
    
    def process_data(self, data: List[str]) -> Dict[str, int]:
        """Process a list of data items."""
        result = defaultdict(int)
        
        for item in data:
            processed_item = self.clean_data(item)
            if processed_item:
                result[processed_item] += 1
                self.processed_count += 1
        
        return dict(result)
    
    def clean_data(self, item: str) -> Optional[str]:
        """Clean and validate a data item."""
        if not item or not isinstance(item, str):
            return None
        
        cleaned = item.strip().lower()
        return cleaned if len(cleaned) > 0 else None
    
    def get_stats(self) -> Dict[str, int]:
        """Get processing statistics."""
        return {
            "processed_count": self.processed_count,
            "config_items": len(self.config)
        }

class AdvancedProcessor(DataProcessor):
    """An advanced data processor with additional features."""
    
    def __init__(self, name: str, config: Optional[Dict] = None, use_cache: bool = True):
        super().__init__(name, config)
        self.use_cache = use_cache
        self.cache = {}
    
    def process_data(self, data: List[str]) -> Dict[str, int]:
        """Enhanced data processing with caching."""
        if self.use_cache and "data" in self.cache:
            return self.cache["data"]
        
        result = super().process_data(data)
        
        if self.use_cache:
            self.cache["data"] = result
        
        return result
    
    def clear_cache(self):
        """Clear the processing cache."""
        self.cache.clear()

def create_processor(processor_type: str = "basic") -> DataProcessor:
    """Factory function to create data processors."""
    if processor_type == "advanced":
        return AdvancedProcessor("advanced_processor")
    else:
        return DataProcessor("basic_processor")

def load_config(config_path: str = CONFIG_PATH) -> Dict:
    """Load configuration from file."""
    try:
        with open(config_path, 'r') as f:
            import json
            return json.load(f)
    except FileNotFoundError:
        print(f"Config file not found: {config_path}")
        return {}
    except json.JSONDecodeError as e:
        print(f"Invalid JSON in config file: {e}")
        return {}

def process_files(file_paths: List[str], processor: DataProcessor) -> None:
    """Process multiple files using the given processor."""
    for file_path in file_paths:
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                lines = f.readlines()
                result = processor.process_data(lines)
                print(f"Processed {file_path}: {len(result)} unique items")
        else:
            print(f"File not found: {file_path}")

def recursive_fibonacci(n):
    """递归函数示例 - 计算斐波那契数列"""
    if n <= 1:
        return n
    return recursive_fibonacci(n - 1) + recursive_fibonacci(n - 2)  # 递归调用

def string_operations_demo():
    """演示字符串方法调用"""
    text = "Hello World"
    result = text.upper()  # str.upper() 调用
    result = result.lower()  # str.lower() 调用
    parts = result.split(" ")  # str.split() 调用
    joined = "-".join(parts)  # str.join() 调用
    return joined.strip()  # str.strip() 调用

def collection_operations_demo():
    """演示集合类型方法调用"""
    # 列表操作
    my_list = []
    my_list.append("item1")  # list.append() 调用
    my_list.extend(["item2", "item3"])  # list.extend() 调用
    my_list.sort()  # list.sort() 调用

    # 字典操作
    my_dict = {}
    my_dict.update({"key1": "value1"})  # dict.update() 调用
    keys = my_dict.keys()  # dict.keys() 调用
    value = my_dict.get("key1", "default")  # dict.get() 调用

    return my_list, my_dict

def main():
    """Main function to demonstrate the data processing."""
    config = load_config()
    processor = create_processor("advanced")

    sample_data = ["apple", "banana", "apple", "cherry", "banana", "date"]
    result = processor.process_data(sample_data)

    print("Processing results:")
    for item, count in result.items():
        print(f"  {item}: {count}")

    stats = processor.get_stats()
    print(f"Statistics: {stats}")

    # 测试递归函数
    fib_result = recursive_fibonacci(5)
    print(f"Fibonacci(5): {fib_result}")

    # 测试字符串操作
    string_result = string_operations_demo()
    print(f"String operations result: {string_result}")

    # 测试集合操作
    list_result, dict_result = collection_operations_demo()
    print(f"Collection operations: {list_result}, {dict_result}")

if __name__ == "__main__":
    main()
