# Python 插件自动配置解决方案

## 问题描述

在使用沙箱IDE进行插件开发时，每次启动都需要重新安装Python Community Edition插件，这是因为：

1. IntelliJ Community Edition默认不包含Python支持
2. 沙箱环境是独立的，不会继承主IDE的插件配置
3. Python插件需要从插件市场下载安装

## 解决方案

### 方案1：自动化脚本设置（推荐）

我们提供了自动化脚本来预配置沙箱环境：

#### Linux/macOS:
```bash
# 运行设置脚本
./scripts/setup-python-plugin.sh

# 或者使用Gradle任务
./gradlew setupPythonPlugin
```

#### Windows:
```cmd
# 运行设置脚本
scripts\setup-python-plugin.bat

# 或者使用Gradle任务
gradlew.bat setupPythonPlugin
```

### 方案2：手动安装（一次性设置）

如果自动化脚本不工作，可以手动设置：

1. **启动沙箱IDE**：
   ```bash
   ./gradlew runIde
   ```

2. **安装Python插件**：
   - 打开 `File` -> `Settings` -> `Plugins`
   - 点击 `Marketplace` 标签
   - 搜索 "Python Community Edition"
   - 点击 `Install` 安装插件
   - 重启IDE

3. **验证安装**：
   - 打开一个 `.py` 文件
   - 确认语法高亮和代码分析正常工作

### 方案3：使用PyCharm Community Edition（备选）

如果需要更好的Python支持，可以修改构建配置：

```kotlin
// 在 build.gradle.kts 中修改
intellij {
    version.set("2024.1.4")
    type.set("PC") // 使用PyCharm Community Edition
    // ...
}
```

## 自动化配置详情

### 脚本功能

自动化脚本会：

1. **创建沙箱目录结构**：
   ```
   build/idea-sandbox-persistent/
   ├── config/
   │   ├── options/
   │   │   ├── ide.general.xml
   │   │   ├── pluginAdvertiser.xml
   │   │   └── filetypes.xml
   │   └── PYTHON_PLUGIN_SETUP.txt
   └── plugins/
   ```

2. **配置文件类型识别**：
   - 预配置 `.py`, `.pyw`, `.pyi` 文件类型
   - 启用Python文件语法识别

3. **配置插件管理器**：
   - 启用插件建议功能
   - 优化插件安装体验

### Gradle任务集成

构建配置中包含以下任务：

- `setupSandbox`: 创建基础沙箱环境
- `setupPythonPlugin`: 运行Python插件配置脚本
- `runIde`: 启动沙箱IDE（依赖上述两个任务）

## 使用流程

### 首次使用

1. **克隆项目并构建**：
   ```bash
   git clone <repository>
   cd demo_plugins
   ./gradlew build
   ```

2. **设置Python插件**：
   ```bash
   ./gradlew setupPythonPlugin
   ```

3. **启动IDE**：
   ```bash
   ./gradlew runIde
   ```

4. **如果Python插件未自动安装**：
   - 按照IDE中的提示安装Python Community Edition插件
   - 或查看 `build/idea-sandbox-persistent/config/PYTHON_PLUGIN_SETUP.txt` 文件

### 后续使用

一旦Python插件安装完成，沙箱环境会保持配置：

```bash
# 直接启动，无需重新配置
./gradlew runIde
```

## 验证Python支持

启动IDE后，可以通过以下方式验证Python支持：

1. **打开测试文件**：
   ```bash
   # 打开项目中的测试Python文件
   open test_python_sample.py
   ```

2. **测试插件功能**：
   - 使用快捷键 `Ctrl+Alt+Shift+T`
   - 或右键选择 "Quick AST Analyzer"
   - 查看 "AST Analysis" 工具窗口中的分析结果

3. **检查分析结果**：
   - 应该能看到Python模块、类、函数的分析
   - 应该能看到函数调用关系
   - 应该能看到递归调用和内置方法调用

## 故障排除

### 问题1：脚本执行失败

**解决方案**：
```bash
# 确保脚本有执行权限
chmod +x scripts/setup-python-plugin.sh

# 手动运行脚本
bash scripts/setup-python-plugin.sh
```

### 问题2：Python插件安装失败

**解决方案**：
1. 检查网络连接
2. 手动从插件市场安装
3. 重启IDE后重试

### 问题3：沙箱环境重置

**解决方案**：
```bash
# 清理并重新设置
rm -rf build/idea-sandbox-persistent
./gradlew setupPythonPlugin runIde
```

## 技术说明

### 沙箱目录持久化

配置使用持久化沙箱目录：
```kotlin
sandboxDir.set("${project.layout.buildDirectory.get()}/idea-sandbox-persistent")
```

这确保了插件配置在多次启动间保持不变。

### 插件兼容性

当前配置支持：
- IntelliJ IDEA Community Edition 2024.1.4
- Python Community Edition插件
- Java 11+ 运行环境

这个解决方案确保了开发体验的一致性，避免了每次启动都需要重新配置Python插件的问题。
