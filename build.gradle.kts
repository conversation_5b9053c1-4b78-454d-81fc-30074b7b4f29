plugins {
    id("java")
    id("org.jetbrains.intellij") version "1.17.2"
}

group = "com.deepcode"
version = "1.0-SNAPSHOT"

// 明确指定Java版本
java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

extensions.configure<org.jetbrains.intellij.IntelliJPluginExtension> {
    version.set("2024.1.4")
    type.set("IC") // 使用Community版本，更小更快
    plugins.set(listOf("com.intellij.java"))
}

tasks {
    buildSearchableOptions {
        enabled = false
    }

    patchPluginXml {
        sinceBuild.set("241")
        untilBuild.set("242.*")
    }

    runIde {
        // 启用自动重载插件
        autoReloadPlugins.set(true)
        // 让Gradle自动下载和配置IDE
        maxHeapSize = "2g"
        // 优化JVM参数，避免版本解析问题
        jvmArgs = listOf(
            "-Xms512m",
            "-Xmx2g",
            "-XX:ReservedCodeCacheSize=512m",
            "-XX:+UseConcMarkSweepGC",
            "-XX:SoftRefLRUPolicyMSPerMB=50",
            "-ea",
            "-XX:CICompilerCount=2",
            "-Dsun.io.useCanonPrefixCache=false",
            "-Djdk.http.auth.tunneling.disabledSchemes=\"\"",
            "-XX:+HeapDumpOnOutOfMemoryError",
            "-XX:-OmitStackTraceInFastThrow",
            // 添加系统属性来避免Gradle版本解析问题
            "-Dgradle.user.home=${System.getProperty("user.home")}/.gradle",
            "-Djava.version=17"
        )
    }

    // 确保编译时使用正确的Java版本
    compileJava {
        options.release.set(17)
    }

    test {
        useJUnitPlatform()
        testLogging {
            events("PASSED", "SKIPPED", "FAILED")
        }
    }
}

configurations.all {
    exclude(group = "org.jetbrains", module = "annotations")
    exclude(group = "org.junit.jupiter")
}

dependencies {
    // 测试依赖已排除，因为在IntelliJ插件环境中不兼容
}