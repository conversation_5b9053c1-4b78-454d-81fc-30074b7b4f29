# AST分析器架构文档

## 概述

本项目提供了一套统一的AST（抽象语法树）分析框架，支持多种编程语言的代码分析。目前支持Java和Python两种语言。

## 架构设计

### 核心组件

#### 1. 数据模型 (`model` 包)

- **AnalysisNode**: 分析节点，表示代码中的可分析单元
  - 支持类、方法、字段、函数、变量等多种节点类型
  - 包含完整的元数据：ID、类型、名称、包名、行号、签名等
  - 新增属性：模块名、文件路径、编程语言
  
- **CallRelation**: 调用关系，表示方法/函数之间的调用关系
  - 支持调用实例聚合，记录所有调用位置
  - 区分内部调用和外部调用
  
- **AnalysisResult**: 分析结果容器
  - 统一的结果格式，支持多语言
  - 包含统计信息和错误信息

#### 2. 分析器 (`analyzer` 包)

- **JavaASTAnalyzer**: Java文件专用分析器
- **PythonASTAnalyzer**: Python文件专用分析器

### 特性对比

| 特性 | JavaASTAnalyzer | PythonASTAnalyzer |
|------|----------------|-------------------|
| 类分析 | ✅ 支持类、接口、枚举 | ✅ 支持类定义 |
| 方法/函数分析 | ✅ 支持方法签名解析 | ✅ 支持函数参数解析 |
| 字段/变量分析 | ✅ 支持字段类型解析 | ✅ 支持模块级变量 |
| 调用关系分析 | ✅ 基于PSI的精确解析 | ✅ 基于正则的模式匹配 |
| 模块识别 | ✅ Maven模块自动识别 | ✅ Python包结构识别 |
| 外部调用检测 | ✅ 智能区分项目内外调用 | ✅ 基于函数名匹配 |
| 线程安全 | ✅ ReadAction保护 | ✅ ReadAction保护 |

## Java分析器详细功能

### Maven模块识别

JavaASTAnalyzer能够自动识别多模块Maven工程的模块信息：

1. **pom.xml检测**: 向上遍历目录树查找pom.xml文件
2. **artifactId解析**: 解析pom.xml中的artifactId作为模块名
3. **模块信息注入**: 将模块信息添加到所有分析节点中

### 精确行号计算

使用IntelliJ IDEA的文档管理器API获取准确的行号：

```java
com.intellij.openapi.editor.Document document = 
    PsiDocumentManager.getInstance(element.getProject())
        .getDocument(containingFile);
return document.getLineNumber(safeOffset) + 1;
```

### 外部调用识别

智能区分项目内部调用和外部调用：

1. **标准库检测**: 识别Java标准库和常见第三方库
2. **项目范围检查**: 使用ProjectFileIndex检查类是否在项目源码中
3. **临时节点**: 外部方法不添加到节点列表，只用于调用关系

## Python分析器详细功能

### 语法模式匹配

使用正则表达式匹配Python语法结构：

```java
private static final Pattern CLASS_PATTERN = Pattern.compile("^\\s*class\\s+(\\w+)\\s*[\\(:]");
private static final Pattern FUNCTION_PATTERN = Pattern.compile("^\\s*def\\s+(\\w+)\\s*\\(([^)]*)\\)\\s*:");
private static final Pattern VARIABLE_PATTERN = Pattern.compile("^\\s*(\\w+)\\s*=");
```

### Python模块识别

支持多种Python项目结构：

1. **包结构识别**: 通过`__init__.py`文件识别Python包
2. **setup.py解析**: 解析setup.py中的项目名称
3. **文件名回退**: 使用文件名作为模块名的备选方案

### 函数调用分析

基于上下文的函数调用关系分析：

1. **函数上下文跟踪**: 记录当前所在的函数
2. **调用模式匹配**: 识别函数调用表达式
3. **内部调用过滤**: 只记录已知函数之间的调用关系

## 使用示例

### Java文件分析

```java
JavaASTAnalyzer analyzer = new JavaASTAnalyzer();
AnalysisResult result = analyzer.analyze(javaFile);

// 获取所有节点
Map<String, AnalysisNode> nodes = result.getNodes();

// 获取调用关系
List<CallRelation> relations = result.getCallRelations();

// 检查Maven模块信息
for (AnalysisNode node : nodes.values()) {
    String moduleName = node.getModuleName(); // Maven模块名
    String filePath = node.getFilePath();     // 文件路径
    String language = node.getLanguage();     // "Java"
}
```

### Python文件分析

```java
PythonASTAnalyzer analyzer = new PythonASTAnalyzer();
AnalysisResult result = analyzer.analyze(pythonFile);

// 获取Python特有的节点类型
for (AnalysisNode node : result.getNodes().values()) {
    if (node.getType() == AnalysisNode.NodeType.FUNCTION) {
        // Python函数
    } else if (node.getType() == AnalysisNode.NodeType.VARIABLE) {
        // Python变量
    }
}
```

## 调试支持

两个分析器都支持调试模式，通过系统属性启用：

```bash
-Dast.analyzer.debug=true
```

启用后会输出详细的分析过程信息，便于问题排查。

## 扩展性

架构设计支持轻松添加新的编程语言支持：

1. 实现新的分析器类（如`CppASTAnalyzer`）
2. 扩展`AnalysisNode.NodeType`枚举
3. 遵循统一的分析结果格式

## 性能优化

- **ReadAction保护**: 所有PSI访问都在ReadAction中进行，确保线程安全
- **调用关系聚合**: 避免重复的调用关系记录
- **错误处理**: 优雅的异常处理，不影响整体分析流程
- **内存优化**: 外部节点不持久化，减少内存占用

## 已修复的问题

1. **外部方法污染**: 不再将外部方法添加到节点列表中
2. **行号不准确**: 使用文档管理器API获取精确行号
3. **模块信息缺失**: 自动识别并添加Maven模块信息
4. **线程安全**: 所有PSI访问都在ReadAction中进行