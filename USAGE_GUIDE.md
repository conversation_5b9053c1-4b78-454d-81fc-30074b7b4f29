# 开发使用指南

## 快速开发

### 启动开发环境
```bash
# 启动沙盒 IDE（推荐）
./gradlew runIde

# 编译代码
./gradlew compileJava

# 构建插件
./gradlew buildPlugin
```

### 开发流程
1. 修改代码
2. 运行 `./gradlew runIde` 启动沙盒 IDE
3. 在沙盒中测试功能
4. 查看结果和调试信息

## 测试插件功能

### 基本测试
1. 在沙盒 IDE 中打开 `examples/TestJavaFile.java`
2. 右键选择 "Quick AST Analyzer"
3. 查看弹窗显示的分析结果

### 快捷键
- `Ctrl+Alt+Shift+T` - 快速 AST 分析器
- `Ctrl+Alt+T` - 显示 AST 分析工具窗口
- `Ctrl+Alt+A` - 生成 AST 报告
- `Ctrl+Alt+E` - 导出 AST 报告

## 调试模式

启用详细调试输出：
```bash
# 方式1：JVM 参数
-Dast.analyzer.debug=true

# 方式2：在代码中设置
System.setProperty("ast.analyzer.debug", "true");
```

## 内外部调用判定

### 判定逻辑（以工程为维度）
- **内部调用**：被调用方法的类在当前工程的源码目录中
- **外部调用**：
  - Java标准库（java.*, javax.*, sun.*等）
  - 第三方库（org.apache.*, org.springframework.*等）
  - 依赖库中的类（非源码目录）
  - 无法解析的方法

### 验证方式
```bash
# 启用调试模式查看判定结果
./gradlew runIde -Dast.analyzer.debug=true
```

调试输出会显示每个调用关系是否为外部调用，以及判定的依据。

## 项目配置

### Gradle 配置
- Java 17
- IntelliJ Platform 2024.1.4
- 自动重载插件已启用

### 性能优化
- 并行构建
- 构建缓存
- 守护进程

## 代码组织

### 工程代码
```
src/main/java/com/sankuai/deepcode/astplugin/
├── 核心分析器
├── UI 组件
├── Action 类
└── 数据模型
```

### 测试代码
```
src/test/java/com/sankuai/deepcode/astplugin/
└── 预留给将来的测试代码
```

## 常见问题

### 内外部调用判定不准确
1. 启用调试模式查看判定过程
2. 检查被调用类是否在工程源码目录中
3. 确认第三方库是否正确识别

### 插件未加载
检查 `plugin.xml` 配置和类路径

### 线程安全错误
所有 PSI 访问已在 ReadAction 中进行，应该不会出现此问题

### 调用关系为空
启用调试模式查看详细分析过程

## 代码质量

- ✅ 测试代码已从工程代码中分离
- ✅ 统一的分析器架构
- ✅ 清晰的包结构
- ✅ 线程安全的实现
- ✅ 准确的行号计算

简洁高效的开发环境，专注于核心功能实现。