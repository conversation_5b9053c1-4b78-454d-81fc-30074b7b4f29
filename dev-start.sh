#!/bin/bash

# IntelliJ 插件快速开发启动脚本

echo "🚀 Starting IntelliJ Plugin Development Environment..."
echo "=================================================="

# 检查是否有正在运行的沙盒IDE
if pgrep -f "runIde" > /dev/null; then
    echo "⚠️  Sandbox IDE is already running. Stopping it first..."
    pkill -f "runIde"
    sleep 2
fi

# 清理并重新构建（可选）
if [ "$1" = "clean" ]; then
    echo "🧹 Cleaning build cache..."
    ./gradlew clean
fi

# 启动沙盒IDE
echo "🔧 Building and starting sandbox IDE..."
echo "📝 Usage Tips:"
echo "   - The sandbox IDE will open in a few moments"
echo "   - Open examples/TestJavaFile.java in the sandbox"
echo "   - Right-click and select 'Quick AST Analyzer'"
echo "   - Or use Ctrl+Alt+Shift+T shortcut"
echo "   - Check console output for debug information"
echo ""
echo "🔍 Debug Mode:"
echo "   - Add -Dast.analyzer.debug=true to enable detailed logging"
echo "   - This will show line numbers for method definitions and calls"
echo "   - Verify line numbers match the actual source code"
echo ""
echo "🔍 Internal/External Call Detection:"
echo "   - Internal calls: methods within current project source"
echo "   - External calls: standard library, third-party libs, dependencies"
echo "   - Debug output shows detection logic and results"
echo ""

./gradlew runIde