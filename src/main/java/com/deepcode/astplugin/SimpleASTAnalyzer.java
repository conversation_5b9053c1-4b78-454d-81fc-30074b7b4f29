package com.deepcode.astplugin;

import com.deepcode.astplugin.analyzer.JavaASTAnalyzer;
import com.deepcode.astplugin.model.AnalysisNode;
import com.deepcode.astplugin.model.AnalysisResult;
import com.deepcode.astplugin.model.CallRelation;
import com.intellij.lang.Language;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.psi.PsiJavaFile;

import java.util.*;

public class SimpleASTAnalyzer {
    private final JavaASTAnalyzer javaAnalyzer = new JavaASTAnalyzer();

    public String generateASTReport(PsiFile psiFile) {
        try {
            Language language = psiFile.getLanguage();
            String languageId = language.getID();

            AnalysisResult result;

            // 根据文件类型分流到不同的分析器
            if ("JAVA".equals(languageId) && psiFile instanceof PsiJavaFile) {
                result = javaAnalyzer.analyze((PsiJavaFile) psiFile);
            } else {
                result = analyzeGenericFile(psiFile);
            }

            return formatAnalysisResult(result);

        } catch (Exception e) {
            return "Error during analysis: " + e.getMessage();
        }
    }

    private AnalysisResult analyzeGenericFile(PsiFile psiFile) {
        AnalysisResult result = new AnalysisResult(psiFile.getName(),
                                                  psiFile.getLanguage().getDisplayName());

        // 基本文件信息分析
        result.updateStatistics("file_size", psiFile.getTextLength());

        // 统计PSI元素类型
        Collection<PsiElement> allElements =
            com.intellij.psi.util.PsiTreeUtil.findChildrenOfType(psiFile, PsiElement.class);
        result.updateStatistics("total_elements", allElements.size());

        return result;
    }

    private String formatAnalysisResult(AnalysisResult result) {
        StringBuilder report = new StringBuilder();

        // 基本信息
        appendHeader(result, report);

        // 节点信息
        appendNodesAnalysis(result, report);

        // 调用关系分析
        appendCallRelationsAnalysis(result, report);

        // 统计信息
        appendStatistics(result, report);

        // 错误信息
        appendErrors(result, report);

        return report.toString();
    }

    private void appendHeader(AnalysisResult result, StringBuilder report) {
        report.append("=== AST Analysis Report ===\n");
        report.append("File: ").append(result.getFileName()).append("\n");
        report.append("Language: ").append(result.getLanguage()).append("\n");
        report.append("Timestamp: ").append(result.getTimestamp()).append("\n");
        report.append("\n");
    }

    private void appendNodesAnalysis(AnalysisResult result, StringBuilder report) {
        Map<String, AnalysisNode> nodes = result.getNodes();
        if (nodes.isEmpty()) return;

        report.append("=== Code Structure ===\n");

        // 按类型分组显示节点
        Map<AnalysisNode.NodeType, List<AnalysisNode>> nodesByType = new HashMap<>();
        for (AnalysisNode node : nodes.values()) {
            nodesByType.computeIfAbsent(node.getType(), k -> new ArrayList<>()).add(node);
        }

        for (AnalysisNode.NodeType type : AnalysisNode.NodeType.values()) {
            List<AnalysisNode> typeNodes = nodesByType.get(type);
            if (typeNodes != null && !typeNodes.isEmpty()) {
                report.append(type.name()).append("S (").append(typeNodes.size()).append("):\n");
                typeNodes.stream()
                    .sorted(Comparator.comparing(AnalysisNode::getSignature))
                    .forEach(node -> report.append("  ").append(node.toString()).append("\n"));
                report.append("\n");
            }
        }
    }

    private void appendCallRelationsAnalysis(AnalysisResult result, StringBuilder report) {
        List<CallRelation> relations = result.getCallRelations();
        if (relations.isEmpty()) return;

        report.append("=== Call Relations Analysis ===\n");
        report.append("Total call relations: ").append(relations.size()).append("\n\n");

        // 调用关系详情
        report.append("Call Relations:\n");
        relations.stream()
            .sorted(Comparator.comparing(r -> r.getCaller().getSignature()))
            .forEach(relation -> {
                report.append("  ").append(relation.toString()).append("\n");
            });
        report.append("\n");

        // 调用统计
        appendCallStatistics(result, report);
    }

    private void appendCallStatistics(AnalysisResult result, StringBuilder report) {
        List<CallRelation> relations = result.getCallRelations();

        // 最活跃的调用者
        Map<String, Long> callerCounts = relations.stream()
            .collect(java.util.stream.Collectors.groupingBy(
                r -> r.getCaller().getSignature(),
                java.util.stream.Collectors.counting()
            ));

        if (!callerCounts.isEmpty()) {
            report.append("Most Active Callers:\n");
            callerCounts.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(5)
                .forEach(entry -> report.append("  ")
                    .append(entry.getKey())
                    .append(" → ")
                    .append(entry.getValue())
                    .append(" calls\n"));
            report.append("\n");
        }

        // 最常被调用的方法
        Map<String, Long> calleeCounts = relations.stream()
            .collect(java.util.stream.Collectors.groupingBy(
                r -> r.getCallee().getSignature(),
                java.util.stream.Collectors.counting()
            ));

        if (!calleeCounts.isEmpty()) {
            report.append("Most Called Methods:\n");
            calleeCounts.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(5)
                .forEach(entry -> report.append("  ")
                    .append(entry.getKey())
                    .append(" ← ")
                    .append(entry.getValue())
                    .append(" calls\n"));
            report.append("\n");
        }

        // 外部调用统计
        long externalCalls = relations.stream()
            .mapToLong(r -> r.isExternal() ? 1 : 0)
            .sum();

        if (externalCalls > 0) {
            report.append("External calls: ").append(externalCalls).append("\n\n");
        }
    }

    private void appendStatistics(AnalysisResult result, StringBuilder report) {
        Map<String, Integer> statistics = result.getStatistics();
        if (statistics.isEmpty()) return;

        report.append("=== Statistics ===\n");
        statistics.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> report.append(entry.getKey())
                .append(": ")
                .append(entry.getValue())
                .append("\n"));
        report.append("\n");
    }

    private void appendErrors(AnalysisResult result, StringBuilder report) {
        List<String> errors = result.getErrors();
        if (errors.isEmpty()) return;

        report.append("=== Errors ===\n");
        errors.forEach(error -> report.append("ERROR: ").append(error).append("\n"));
        report.append("\n");
    }
}
