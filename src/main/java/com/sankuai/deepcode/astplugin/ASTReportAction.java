package com.sankuai.deepcode.astplugin;

import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.CommonDataKeys;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.project.Project;
import com.intellij.psi.PsiFile;
import com.intellij.ui.JBColor;
import com.intellij.ui.components.JBScrollPane;
import com.intellij.ui.components.JBTextArea;
import javax.swing.*;
import java.awt.*;

public class ASTReportAction extends AnAction {

    @Override
    public void actionPerformed(AnActionEvent e) {
        Project project = e.getProject();
        Editor editor = e.getData(CommonDataKeys.EDITOR);
        PsiFile psiFile = e.getData(CommonDataKeys.PSI_FILE);

        if (project == null || editor == null || psiFile == null) {
            JOptionPane.showMessageDialog(null, "No file selected or project context missing", "Error", JOptionPane.ERROR_MESSAGE);
            return;
        }

        // Generate AST report using ASTAnalyzer
        DeepCodeASTAnalyzer analyzer = new DeepCodeASTAnalyzer();
        String report = analyzer.generateASTReport(psiFile);

        // Display report in a dialog
        showReportDialog(project, report, psiFile.getName());
    }

    private void showReportDialog(Project project, String report, String fileName) {
        JDialog dialog = new JDialog();
        dialog.setTitle("AST Report - " + fileName);
        dialog.setSize(800, 600);
        dialog.setLocationRelativeTo(null);
        dialog.setModal(true);

        JBTextArea textArea = new JBTextArea(report);
        textArea.setEditable(false);
        textArea.setBackground(JBColor.WHITE);
        textArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));

        dialog.add(new JBScrollPane(textArea), BorderLayout.CENTER);

        JButton closeButton = new JButton("Close");
        closeButton.addActionListener(e -> dialog.dispose());

        JPanel buttonPanel = new JPanel();
        buttonPanel.add(closeButton);
        dialog.add(buttonPanel, BorderLayout.SOUTH);

        dialog.setVisible(true);
    }

    @Override
    public void update(AnActionEvent e) {
        // Enable action only when a file is open
        PsiFile psiFile = e.getData(CommonDataKeys.PSI_FILE);
        e.getPresentation().setEnabled(psiFile != null);
    }
}