package com.sankuai.deepcode.astplugin.model;

/**
 * Language枚举功能测试
 */
public class LanguageTest {
    public static void main(String[] args) {
        // 测试从文件名推断语言
        System.out.println("=== 从文件名推断语言 ===");
        testFromFileName("Main.java", Language.JAVA);
        testFromFileName("script.py", Language.PYTHON);
        testFromFileName("app.js", Language.JAVASCRIPT);
        testFromFileName("component.ts", Language.TYPESCRIPT);
        testFromFileName("Service.kt", Language.KOTLIN);
        testFromFileName("unknown.txt", Language.UNKNOWN);
        
        // 测试从语言代码获取枚举
        System.out.println("\n=== 从语言代码获取枚举 ===");
        testFromCode("java", Language.JAVA);
        testFromCode("python", Language.PYTHON);
        testFromCode("JAVASCRIPT", Language.JAVASCRIPT);
        testFromCode("invalid", Language.UNKNOWN);
        
        // 测试AnalysisResult构造函数
        System.out.println("\n=== 测试AnalysisResult构造函数 ===");
        testAnalysisResult();
    }
    
    private static void testFromFileName(String fileName, Language expected) {
        Language actual = Language.fromFileName(fileName);
        System.out.printf("文件名: %-15s -> 语言: %-10s (期望: %s) %s%n", 
            fileName, actual.getDisplayName(), expected.getDisplayName(),
            actual == expected ? "✓" : "✗");
    }
    
    private static void testFromCode(String code, Language expected) {
        Language actual = Language.fromCode(code);
        System.out.printf("代码: %-15s -> 语言: %-10s (期望: %s) %s%n", 
            code, actual.getDisplayName(), expected.getDisplayName(),
            actual == expected ? "✓" : "✗");
    }
    
    private static void testAnalysisResult() {
        // 使用Language枚举构造
        AnalysisResult result1 = new AnalysisResult("Test.java", Language.JAVA);
        System.out.printf("直接使用枚举: %s - %s%n", 
            result1.getFileName(), result1.getLanguageDisplayName());
        
        // 使用字符串代码构造
        AnalysisResult result2 = new AnalysisResult("test.py", "python");
        System.out.printf("使用字符串代码: %s - %s%n", 
            result2.getFileName(), result2.getLanguageDisplayName());
        
        // 仅使用文件名构造（自动推断）
        AnalysisResult result3 = new AnalysisResult("app.js");
        System.out.printf("自动推断语言: %s - %s%n", 
            result3.getFileName(), result3.getLanguageDisplayName());
    }
}