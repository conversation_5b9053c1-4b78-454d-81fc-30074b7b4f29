package com.sankuai.deepcode.astplugin.analyzer;

import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.psi.util.PsiTreeUtil;
import com.sankuai.deepcode.astplugin.model.AnalysisNode;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.CallRelation;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Python文件专用AST分析器
 * 
 * 特性：
 * - 支持Python函数、类、变量分析
 * - 支持Python模块识别
 * - 支持函数调用关系分析
 * - 线程安全的PSI访问
 * - 基于PSI接口的精确解析
 *
 * <AUTHOR>
 */
public class PythonASTAnalyzer {
    
    private static final boolean DEBUG_MODE = Boolean.getBoolean("ast.analyzer.debug");
    
    // 保留setup.py解析的正则表达式
    private static final Pattern SETUP_PY_NAME_PATTERN = Pattern.compile("name\\s*=\\s*['\"]([^'\"]+)['\"]");

    /**
     * 分析Python文件，返回完整的分析结果
     */
    public AnalysisResult analyze(PsiFile pythonFile) {
        return ReadAction.compute(() -> {
            AnalysisResult result = new AnalysisResult(pythonFile.getName(), "Python");
            
            try {
                debugLog("Starting Python analysis for file: " + pythonFile.getName());
                
                // 获取模块信息
                String moduleName = detectPythonModule(pythonFile);
                String filePath = getFilePath(pythonFile);
                
                // 使用PSI接口分析Python文件
                analyzePythonFile(pythonFile, result, moduleName, filePath);

                debugLog("Python analysis completed successfully. Found " + 
                        result.getNodes().size() + " nodes and " + 
                        result.getCallRelations().size() + " call relations");
                
            } catch (Exception e) {
                String errorMsg = "Python analysis failed for " + pythonFile.getName() + ": " + e.getMessage();
                result.addError(errorMsg);
                debugLog("ERROR: " + errorMsg);
                if (DEBUG_MODE) {
                    e.printStackTrace();
                }
            }
            
            return result;
        });
    }

    /**
     * 检测Python模块名 - 基于项目根目录的完整模块路径
     */
    private String detectPythonModule(PsiFile pythonFile) {
        try {
            VirtualFile virtualFile = pythonFile.getVirtualFile();
            if (virtualFile == null) {
                return pythonFile.getName().replace(".py", "");
            }

            Project project = pythonFile.getProject();
            VirtualFile projectRoot = project.getBaseDir();

            if (projectRoot == null) {
                return virtualFile.getNameWithoutExtension();
            }

            // 计算从项目根目录到文件的相对路径
            String relativePath = getRelativePathFromProject(virtualFile, projectRoot);
            if (relativePath == null) {
                return virtualFile.getNameWithoutExtension();
            }

            // 构建Python模块路径
            String modulePath = buildPythonModulePath(relativePath, virtualFile);
            debugLog("Detected Python module path: " + modulePath);
            return modulePath;

        } catch (Exception e) {
            debugLog("Error detecting Python module: " + e.getMessage());
            return pythonFile.getName().replace(".py", "");
        }
    }

    /**
     * 获取文件相对于项目根目录的路径
     */
    private String getRelativePathFromProject(VirtualFile file, VirtualFile projectRoot) {
        String filePath = file.getPath();
        String projectPath = projectRoot.getPath();

        if (filePath.startsWith(projectPath)) {
            return filePath.substring(projectPath.length() + 1);
        }
        return null;
    }

    /**
     * 构建Python模块路径
     */
    private String buildPythonModulePath(String relativePath, VirtualFile file) {
        // 移除文件扩展名
        String pathWithoutExtension = relativePath.replace(".py", "");

        // 将路径分隔符替换为点号
        String modulePath = pathWithoutExtension.replace("/", ".").replace("\\", ".");

        // 如果是__init__.py文件，移除最后的__init__
        if (modulePath.endsWith(".__init__")) {
            modulePath = modulePath.substring(0, modulePath.length() - 9);
        }

        // 如果模块路径为空，使用文件名
        if (modulePath.isEmpty()) {
            modulePath = file.getNameWithoutExtension();
        }

        return modulePath;
    }

    /**
     * 解析setup.py文件获取项目名
     */
    private String parseSetupPyName(com.intellij.openapi.vfs.VirtualFile setupFile) {
        try {
            String content = new String(setupFile.contentsToByteArray());
            Matcher matcher = SETUP_PY_NAME_PATTERN.matcher(content);
            if (matcher.find()) {
                return matcher.group(1);
            }
        } catch (Exception e) {
            debugLog("Error parsing setup.py: " + e.getMessage());
        }
        return null;
    }

    /**
     * 获取文件路径
     */
    private String getFilePath(PsiFile pythonFile) {
        try {
            com.intellij.openapi.vfs.VirtualFile virtualFile = pythonFile.getVirtualFile();
            if (virtualFile != null) {
                return virtualFile.getPath();
            }
        } catch (Exception e) {
            debugLog("Error getting file path: " + e.getMessage());
        }
        return pythonFile.getName();
    }

    /**
     * 使用增强的PSI接口分析Python文件
     */
    private void analyzePythonFile(PsiFile pythonFile, AnalysisResult result, String moduleName, String filePath) {
        try {
            // 存储所有函数节点，用于调用关系分析
            Map<String, AnalysisNode> functions = new HashMap<>();

            // 分析导入语句
            analyzePythonImports(pythonFile, result);

            // 分析类定义
            analyzePythonClasses(pythonFile, result, moduleName, filePath, functions);

            // 分析模块级函数
            analyzePythonModuleFunctions(pythonFile, result, moduleName, filePath, functions);

            // 分析模块级变量
            analyzePythonModuleVariables(pythonFile, result, moduleName, filePath);

            // 分析函数调用关系
            analyzePythonCallRelations(pythonFile, result, functions);

        } catch (Exception e) {
            debugLog("Error analyzing Python file with enhanced PSI: " + e.getMessage());
            result.addError("Enhanced PSI analysis error: " + e.getMessage());
        }
    }

    /**
     * 分析Python导入语句 - 使用增强的PSI分析
     */
    private void analyzePythonImports(PsiFile pythonFile, AnalysisResult result) {
        try {
            // 使用PsiTreeUtil查找所有可能的导入语句
            Collection<PsiElement> allElements = PsiTreeUtil.findChildrenOfType(pythonFile, PsiElement.class);
            int importCount = 0;

            for (PsiElement element : allElements) {
                if (isPythonImportStatement(element)) {
                    importCount++;
                    debugLog("Found Python import statement: " + element.getText().trim());
                }
            }

            if (importCount > 0) {
                result.updateStatistics("imports", importCount);
                debugLog("Found " + importCount + " Python import statements");
            }
        } catch (Exception e) {
            debugLog("Error analyzing Python imports: " + e.getMessage());
        }
    }

    /**
     * 分析Python类定义 - 使用增强的PSI分析
     */
    private void analyzePythonClasses(PsiFile pythonFile, AnalysisResult result, String moduleName, String filePath, Map<String, AnalysisNode> functions) {
        try {
            // 使用PsiTreeUtil查找所有可能的类定义
            Collection<PsiElement> allElements = PsiTreeUtil.findChildrenOfType(pythonFile, PsiElement.class);

            for (PsiElement element : allElements) {
                if (isPythonClassDefinition(element)) {
                    analyzePythonClassElement(element, result, moduleName, filePath, functions);
                }
            }
        } catch (Exception e) {
            debugLog("Error analyzing Python classes: " + e.getMessage());
        }
    }

    /**
     * 分析单个Python类元素
     */
    private void analyzePythonClassElement(PsiElement classElement, AnalysisResult result, String moduleName, String filePath, Map<String, AnalysisNode> functions) {
        try {
            // 使用增强的PSI方法提取类名
            String className = extractPythonClassName(classElement);
            if (className != null) {
                int lineNumber = getElementLineNumber(classElement);

                String classId = generateClassId(className, moduleName);
                AnalysisNode classNode = new AnalysisNode(
                        classId,
                        AnalysisNode.NodeType.CLASS,
                        className,
                        className,
                        moduleName != null ? moduleName : "",
                        lineNumber,
                        generateClassSignature(className, moduleName),
                        moduleName,
                        filePath,
                        "Python"
                );

                result.addNode(classNode);
                result.incrementStatistics("classes");
                debugLog("Added Python class: " + classId + " at line " + lineNumber);

                // 分析类中的方法
                analyzePythonClassMethods(classElement, className, result, moduleName, filePath, functions);
            }
        } catch (Exception e) {
            debugLog("Error analyzing Python class element: " + e.getMessage());
        }
    }

    /**
     * 分析Python类中的方法
     */
    private void analyzePythonClassMethods(PsiElement classElement, String className, AnalysisResult result, String moduleName, String filePath, Map<String, AnalysisNode> functions) {
        try {
            // 使用PsiTreeUtil查找类中的所有函数定义
            Collection<PsiElement> methodElements = PsiTreeUtil.findChildrenOfType(classElement, PsiElement.class);

            for (PsiElement element : methodElements) {
                if (isPythonFunctionDefinition(element)) {
                    String methodName = extractPythonFunctionName(element);
                    String parameters = extractPythonFunctionParameters(element);

                    if (methodName != null) {
                        int lineNumber = getElementLineNumber(element);

                        String methodId = generateFunctionId(methodName, className, moduleName, parameters);
                        AnalysisNode methodNode = new AnalysisNode(
                                methodId,
                                AnalysisNode.NodeType.FUNCTION,
                                methodName,
                                className,
                                moduleName != null ? moduleName : "",
                                lineNumber,
                                generateFunctionSignature(methodName, className, moduleName, parameters),
                                moduleName,
                                filePath,
                                "Python"
                        );

                        result.addNode(methodNode);
                        functions.put(methodName, methodNode);
                        result.incrementStatistics("functions");
                        debugLog("Added Python method: " + methodId + " at line " + lineNumber);
                    }
                }
            }
        } catch (Exception e) {
            debugLog("Error analyzing Python class methods: " + e.getMessage());
        }
    }

    /**
     * 分析Python模块级函数
     */
    private void analyzePythonModuleFunctions(PsiFile pythonFile, AnalysisResult result, String moduleName, String filePath, Map<String, AnalysisNode> functions) {
        try {
            // 使用PsiTreeUtil查找所有顶级函数定义
            Collection<PsiElement> allElements = PsiTreeUtil.findChildrenOfType(pythonFile, PsiElement.class);

            for (PsiElement element : allElements) {
                if (isPythonFunctionDefinition(element) && isTopLevelElement(element, pythonFile)) {
                    String functionName = extractPythonFunctionName(element);
                    String parameters = extractPythonFunctionParameters(element);

                    if (functionName != null) {
                        int lineNumber = getElementLineNumber(element);

                        String functionId = generateFunctionId(functionName, null, moduleName, parameters);
                        AnalysisNode functionNode = new AnalysisNode(
                                functionId,
                                AnalysisNode.NodeType.FUNCTION,
                                functionName,
                                "",
                                moduleName != null ? moduleName : "",
                                lineNumber,
                                generateFunctionSignature(functionName, null, moduleName, parameters),
                                moduleName,
                                filePath,
                                "Python"
                        );

                        result.addNode(functionNode);
                        functions.put(functionName, functionNode);
                        result.incrementStatistics("functions");
                        debugLog("Added Python function: " + functionId + " at line " + lineNumber);
                    }
                }
            }
        } catch (Exception e) {
            debugLog("Error analyzing Python module functions: " + e.getMessage());
        }
    }

    /**
     * 分析Python模块级变量
     */
    private void analyzePythonModuleVariables(PsiFile pythonFile, AnalysisResult result, String moduleName, String filePath) {
        try {
            // 使用PsiTreeUtil查找所有顶级赋值语句
            Collection<PsiElement> allElements = PsiTreeUtil.findChildrenOfType(pythonFile, PsiElement.class);

            for (PsiElement element : allElements) {
                if (isPythonAssignmentStatement(element) && isTopLevelElement(element, pythonFile)) {
                    String variableName = extractPythonVariableName(element);

                    if (variableName != null && !isSpecialVariable(variableName)) {
                        int lineNumber = getElementLineNumber(element);

                        String variableId = generateVariableId(variableName, moduleName);
                        AnalysisNode variableNode = new AnalysisNode(
                                variableId,
                                AnalysisNode.NodeType.VARIABLE,
                                variableName,
                                "",
                                moduleName != null ? moduleName : "",
                                lineNumber,
                                generateVariableSignature(variableName, moduleName),
                                moduleName,
                                filePath,
                                "Python"
                        );

                        result.addNode(variableNode);
                        result.incrementStatistics("variables");
                        debugLog("Added Python variable: " + variableId + " at line " + lineNumber);
                    }
                }
            }
        } catch (Exception e) {
            debugLog("Error analyzing Python module variables: " + e.getMessage());
        }
    }

    /**
     * 分析Python函数调用关系
     */
    private void analyzePythonCallRelations(PsiFile pythonFile, AnalysisResult result, Map<String, AnalysisNode> functions) {
        debugLog("Starting Python call relation analysis");
        debugLog("Available functions: " + functions.keySet());

        try {
            Map<String, List<CallRelation.CallInstance>> aggregatedCalls = new HashMap<>();

            // 递归遍历所有PSI元素查找函数调用
            analyzePythonFunctionCallsRecursively(pythonFile, functions, aggregatedCalls);

            // 将聚合后的调用关系添加到结果中
            for (Map.Entry<String, List<CallRelation.CallInstance>> entry : aggregatedCalls.entrySet()) {
                String[] parts = entry.getKey().split(" -> ");
                if (parts.length == 2) {
                    String callerId = parts[0];
                    String calleeId = parts[1];

                    AnalysisNode caller = findNodeById(result, callerId);
                    AnalysisNode callee = findNodeById(result, calleeId);

                    if (caller != null && callee != null) {
                        List<CallRelation.CallInstance> instances = entry.getValue();
                        CallRelation.CallInstance firstInstance = instances.get(0);

                        CallRelation relation = new CallRelation(
                            caller, callee,
                            firstInstance.getLineNumber(),
                            firstInstance.getExpression(),
                            false, // 内部调用
                            instances
                        );

                        result.addCallRelation(relation);
                        result.incrementStatistics("function_calls");
                    }
                }
            }

            debugLog("Python call relation analysis completed. Found " + aggregatedCalls.size() + " unique relations");

        } catch (Exception e) {
            debugLog("Error analyzing Python call relations: " + e.getMessage());
        }
    }

    /**
     * 递归分析函数调用
     */
    private void analyzeFunctionCallsRecursively(PsiElement element, Map<String, AnalysisNode> functions,
                                               Map<String, List<CallRelation.CallInstance>> aggregatedCalls) {
        try {
            // 检查当前元素是否包含函数调用
            if (isPotentialFunctionCall(element)) {
                analyzeFunctionCallElement(element, functions, aggregatedCalls);
            }

            // 递归处理子元素
            PsiElement[] children = element.getChildren();
            for (PsiElement child : children) {
                analyzeFunctionCallsRecursively(child, functions, aggregatedCalls);
            }
        } catch (Exception e) {
            debugLog("Error in recursive function call analysis: " + e.getMessage());
        }
    }

    /**
     * 检查是否为潜在的函数调用
     */
    private boolean isPotentialFunctionCall(PsiElement element) {
        String elementText = element.getText();
        if (elementText == null) {
            return false;
        }

        // 检查是否包含函数调用模式
        return elementText.matches(".*\\w+\\s*\\(.*\\).*") &&
               !elementText.trim().startsWith("def ") &&
               !elementText.trim().startsWith("class ");
    }

    /**
     * 分析函数调用元素
     */
    private void analyzeFunctionCallElement(PsiElement element, Map<String, AnalysisNode> functions,
                                          Map<String, List<CallRelation.CallInstance>> aggregatedCalls) {
        try {
            // 查找调用者函数
            AnalysisNode callerFunction = findContainingFunction(element, functions);
            if (callerFunction == null) return;

            String elementText = element.getText();
            if (elementText == null) return;

            // 使用更精确的方式查找函数调用
            Pattern callPattern = Pattern.compile("(\\w+)\\s*\\(");
            Matcher matcher = callPattern.matcher(elementText);

            while (matcher.find()) {
                String functionName = matcher.group(1);

                // 检查是否为已知函数且不是调用者自身
                if (functions.containsKey(functionName) &&
                    !functionName.equals(callerFunction.getName()) &&
                    !isPythonBuiltinFunction(functionName)) {

                    AnalysisNode calleeFunction = functions.get(functionName);
                    String aggregationKey = callerFunction.getId() + " -> " + calleeFunction.getId();

                    List<CallRelation.CallInstance> instances = aggregatedCalls.computeIfAbsent(
                        aggregationKey, k -> new ArrayList<>());

                    instances.add(new CallRelation.CallInstance(
                        getElementLineNumber(element),
                        elementText.trim()
                    ));

                    debugLog("Found function call: " + callerFunction.getName() + " -> " + functionName + " at line " + getElementLineNumber(element));
                }
            }

        } catch (Exception e) {
            debugLog("Error analyzing function call element: " + e.getMessage());
        }
    }

    /**
     * 查找包含指定元素的函数
     */
    private AnalysisNode findContainingFunction(PsiElement element, Map<String, AnalysisNode> functions) {
        PsiElement current = element.getParent();

        while (current != null) {
            if (isFunctionDefinition(current)) {
                String functionName = extractFunctionNameFromPsi(current);
                if (functionName != null && functions.containsKey(functionName)) {
                    return functions.get(functionName);
                }
            }
            current = current.getParent();
        }

        return null;
    }



    /**
     * 检查是否为特殊变量（如__name__, __file__等）
     */
    private boolean isSpecialVariable(String variableName) {
        return variableName.startsWith("__") && variableName.endsWith("__") ||
               variableName.equals("self") || variableName.equals("cls");
    }

    // ==================== Python PSI识别方法 ====================

    /**
     * 检查PSI元素是否为Python导入语句 - 增强版本
     */
    private boolean isPythonImportStatement(PsiElement element) {
        if (element == null) return false;

        String text = element.getText();
        if (text == null) return false;

        String trimmed = text.trim();

        // 检查是否为导入语句的开始
        if (trimmed.startsWith("import ") || trimmed.startsWith("from ")) {
            // 确保是完整的导入语句，不是注释或字符串中的内容
            String[] lines = trimmed.split("\n");
            String firstLine = lines[0].trim();

            // 基本的导入语句模式检查
            if (firstLine.startsWith("import ")) {
                return firstLine.matches("import\\s+[a-zA-Z_][a-zA-Z0-9_]*(?:\\.[a-zA-Z_][a-zA-Z0-9_]*)*(?:\\s+as\\s+[a-zA-Z_][a-zA-Z0-9_]*)?(?:\\s*,\\s*[a-zA-Z_][a-zA-Z0-9_]*(?:\\.[a-zA-Z_][a-zA-Z0-9_]*)*(?:\\s+as\\s+[a-zA-Z_][a-zA-Z0-9_]*)?)*");
            } else if (firstLine.startsWith("from ")) {
                return firstLine.matches("from\\s+[a-zA-Z_][a-zA-Z0-9_]*(?:\\.[a-zA-Z_][a-zA-Z0-9_]*)*\\s+import\\s+(?:\\*|[a-zA-Z_][a-zA-Z0-9_]*(?:\\s+as\\s+[a-zA-Z_][a-zA-Z0-9_]*)?(?:\\s*,\\s*[a-zA-Z_][a-zA-Z0-9_]*(?:\\s+as\\s+[a-zA-Z_][a-zA-Z0-9_]*)?)*)");
            }
        }

        return false;
    }

    /**
     * 检查PSI元素是否为Python类定义 - 增强版本
     */
    private boolean isPythonClassDefinition(PsiElement element) {
        if (element == null) return false;

        String text = element.getText();
        if (text == null) return false;

        String trimmed = text.trim();
        String[] lines = trimmed.split("\n");
        String firstLine = lines[0].trim();

        // 检查类定义模式
        if (firstLine.startsWith("class ")) {
            // 更精确的类定义检查
            return firstLine.matches("class\\s+[a-zA-Z_][a-zA-Z0-9_]*(?:\\s*\\([^)]*\\))?\\s*:");
        }

        return false;
    }

    /**
     * 检查PSI元素是否为Python函数定义 - 增强版本
     */
    private boolean isPythonFunctionDefinition(PsiElement element) {
        if (element == null) return false;

        String text = element.getText();
        if (text == null) return false;

        String trimmed = text.trim();
        String[] lines = trimmed.split("\n");
        String firstLine = lines[0].trim();

        // 检查函数定义模式
        if (firstLine.startsWith("def ")) {
            // 更精确的函数定义检查，支持装饰器
            return firstLine.matches("def\\s+[a-zA-Z_][a-zA-Z0-9_]*\\s*\\([^)]*\\)\\s*(?:->\\s*[^:]+)?\\s*:");
        }

        return false;
    }

    /**
     * 检查PSI元素是否为Python赋值语句 - 增强版本
     */
    private boolean isPythonAssignmentStatement(PsiElement element) {
        if (element == null) return false;

        String text = element.getText();
        if (text == null) return false;

        String trimmed = text.trim();

        // 排除非赋值语句
        if (trimmed.startsWith("def ") || trimmed.startsWith("class ") ||
            trimmed.startsWith("import ") || trimmed.startsWith("from ") ||
            trimmed.startsWith("#") || trimmed.startsWith("\"\"\"") || trimmed.startsWith("'''")) {
            return false;
        }

        // 检查是否包含赋值操作符，但排除比较操作符
        if (trimmed.contains("=")) {
            // 更精确的赋值检查
            return trimmed.matches(".*[a-zA-Z_][a-zA-Z0-9_]*\\s*=\\s*.*") &&
                   !trimmed.contains("==") && !trimmed.contains("!=") &&
                   !trimmed.contains("<=") && !trimmed.contains(">=") &&
                   !trimmed.contains("+=") && !trimmed.contains("-=") &&
                   !trimmed.contains("*=") && !trimmed.contains("/=") &&
                   !trimmed.contains("//=") && !trimmed.contains("%=") &&
                   !trimmed.contains("**=") && !trimmed.contains("&=") &&
                   !trimmed.contains("|=") && !trimmed.contains("^=") &&
                   !trimmed.contains(">>=") && !trimmed.contains("<<=");
        }

        return false;
    }

    /**
     * 检查PSI元素是否为函数定义
     */
    private boolean isFunctionDefinition(PsiElement element) {
        if (element == null) return false;

        // 检查元素类型名称
        String elementType = element.getClass().getSimpleName();
        if (elementType.contains("PyFunction") || elementType.contains("Function")) {
            return true;
        }

        // 备用方案：检查文本内容
        String text = element.getText();
        if (text != null) {
            String firstLine = text.split("\n")[0].trim();
            return firstLine.startsWith("def ") && firstLine.contains("(") && firstLine.contains(":");
        }

        return false;
    }

    /**
     * 检查PSI元素是否为赋值语句（变量定义）
     */
    private boolean isAssignmentStatement(PsiElement element) {
        if (element == null) return false;

        // 检查元素类型名称
        String elementType = element.getClass().getSimpleName();
        if (elementType.contains("PyAssignment") || elementType.contains("Assignment")) {
            return true;
        }

        // 备用方案：检查文本内容
        String text = element.getText();
        if (text != null) {
            String trimmed = text.trim();
            return trimmed.contains("=") &&
                   !trimmed.startsWith("def ") &&
                   !trimmed.startsWith("class ") &&
                   !trimmed.startsWith("#");
        }

        return false;
    }

    /**
     * 检查PSI元素是否为导入语句
     */
    private boolean isImportStatement(PsiElement element) {
        if (element == null) return false;

        // 检查元素类型名称
        String elementType = element.getClass().getSimpleName();
        if (elementType.contains("PyImport") || elementType.contains("Import")) {
            return true;
        }

        // 备用方案：检查文本内容
        String text = element.getText();
        if (text != null) {
            String trimmed = text.trim();
            return trimmed.startsWith("import ") || trimmed.startsWith("from ");
        }

        return false;
    }

    // ==================== Python PSI元素信息提取方法 ====================

    /**
     * 从PSI元素中提取Python类名
     */
    private String extractPythonClassName(PsiElement classElement) {
        try {
            // 从文本中提取类名
            String text = classElement.getText();
            if (text != null) {
                String firstLine = text.split("\n")[0].trim();
                return extractClassName(firstLine);
            }
        } catch (Exception e) {
            debugLog("Error extracting Python class name from PSI: " + e.getMessage());
        }
        return null;
    }

    /**
     * 从PSI元素中提取Python函数名
     */
    private String extractPythonFunctionName(PsiElement functionElement) {
        try {
            // 从文本中提取函数名
            String text = functionElement.getText();
            if (text != null) {
                String firstLine = text.split("\n")[0].trim();
                return extractFunctionName(firstLine);
            }
        } catch (Exception e) {
            debugLog("Error extracting Python function name from PSI: " + e.getMessage());
        }
        return null;
    }

    /**
     * 从PSI元素中提取Python函数参数
     */
    private String extractPythonFunctionParameters(PsiElement functionElement) {
        try {
            // 从文本中提取参数
            String text = functionElement.getText();
            if (text != null) {
                String firstLine = text.split("\n")[0].trim();
                return extractFunctionParameters(firstLine);
            }
        } catch (Exception e) {
            debugLog("Error extracting Python function parameters from PSI: " + e.getMessage());
        }
        return "";
    }

    /**
     * 从PSI元素中提取Python变量名
     */
    private String extractPythonVariableName(PsiElement assignmentElement) {
        try {
            // 从文本中提取变量名
            String text = assignmentElement.getText();
            if (text != null) {
                return extractVariableName(text.trim());
            }
        } catch (Exception e) {
            debugLog("Error extracting Python variable name from PSI: " + e.getMessage());
        }
        return null;
    }

    /**
     * 从PSI元素中提取函数名
     */
    private String extractFunctionNameFromPsi(PsiElement functionElement) {
        try {
            // 从文本中提取函数名
            String text = functionElement.getText();
            if (text != null) {
                String firstLine = text.split("\n")[0].trim();
                return extractFunctionName(firstLine);
            }
        } catch (Exception e) {
            debugLog("Error extracting function name from PSI: " + e.getMessage());
        }
        return null;
    }

    /**
     * 从PSI元素中提取函数参数
     */
    private String extractFunctionParametersFromPsi(PsiElement functionElement) {
        try {
            // 从文本中提取参数
            String text = functionElement.getText();
            if (text != null) {
                String firstLine = text.split("\n")[0].trim();
                return extractFunctionParameters(firstLine);
            }
        } catch (Exception e) {
            debugLog("Error extracting function parameters from PSI: " + e.getMessage());
        }
        return "";
    }

    /**
     * 从PSI元素中提取变量名
     */
    private String extractVariableNameFromPsi(PsiElement assignmentElement) {
        try {
            // 从文本中提取变量名
            String text = assignmentElement.getText();
            if (text != null) {
                return extractVariableName(text.trim());
            }
        } catch (Exception e) {
            debugLog("Error extracting variable name from PSI: " + e.getMessage());
        }
        return null;
    }

    // ==================== Python PSI辅助方法 ====================

    /**
     * 检查元素是否为顶级元素（直接在文件根级别）
     */
    private boolean isTopLevelElement(PsiElement element, PsiFile file) {
        if (element == null || file == null) return false;

        // 检查父元素是否为文件本身或文件的直接子元素
        PsiElement parent = element.getParent();
        while (parent != null) {
            if (parent == file) {
                return true;
            }
            // 如果父元素是类或函数，则不是顶级元素
            if (isPythonClassDefinition(parent) || isPythonFunctionDefinition(parent)) {
                return false;
            }
            parent = parent.getParent();
        }
        return false;
    }

    /**
     * 递归分析Python函数调用
     */
    private void analyzePythonFunctionCallsRecursively(PsiElement element, Map<String, AnalysisNode> functions,
                                                     Map<String, List<CallRelation.CallInstance>> aggregatedCalls) {
        try {
            // 检查当前元素是否包含函数调用
            if (isPotentialPythonFunctionCall(element)) {
                analyzePythonFunctionCallElement(element, functions, aggregatedCalls);
            }

            // 递归处理子元素
            Collection<PsiElement> children = PsiTreeUtil.findChildrenOfType(element, PsiElement.class);
            for (PsiElement child : children) {
                if (child != element) { // 避免无限递归
                    analyzePythonFunctionCallsRecursively(child, functions, aggregatedCalls);
                }
            }
        } catch (Exception e) {
            debugLog("Error in recursive Python function call analysis: " + e.getMessage());
        }
    }

    /**
     * 检查是否为潜在的Python函数调用 - 增强版本
     */
    private boolean isPotentialPythonFunctionCall(PsiElement element) {
        String elementText = element.getText();
        if (elementText == null) {
            return false;
        }

        String trimmed = elementText.trim();

        // 排除定义语句和注释
        if (trimmed.startsWith("def ") || trimmed.startsWith("class ") ||
            trimmed.startsWith("#") || trimmed.startsWith("\"\"\"") || trimmed.startsWith("'''")) {
            return false;
        }

        // 检查是否包含函数调用模式
        // 支持简单函数调用、方法调用、链式调用等
        return trimmed.matches(".*[a-zA-Z_][a-zA-Z0-9_]*(?:\\.[a-zA-Z_][a-zA-Z0-9_]*)*\\s*\\([^)]*\\).*") &&
               !trimmed.contains("\n"); // 只检查单行调用
    }

    /**
     * 分析Python函数调用元素
     */
    private void analyzePythonFunctionCallElement(PsiElement element, Map<String, AnalysisNode> functions,
                                                Map<String, List<CallRelation.CallInstance>> aggregatedCalls) {
        try {
            // 查找调用者函数
            AnalysisNode callerFunction = findContainingPythonFunction(element, functions);
            if (callerFunction == null) return;

            String elementText = element.getText();
            if (elementText == null) return;

            // 使用更精确的方式查找函数调用
            Pattern callPattern = Pattern.compile("(\\w+)\\s*\\(");
            Matcher matcher = callPattern.matcher(elementText);

            while (matcher.find()) {
                String functionName = matcher.group(1);

                // 检查是否为已知函数且不是调用者自身
                if (functions.containsKey(functionName) &&
                    !functionName.equals(callerFunction.getName()) &&
                    !isPythonBuiltinFunction(functionName)) {

                    AnalysisNode calleeFunction = functions.get(functionName);
                    String aggregationKey = callerFunction.getId() + " -> " + calleeFunction.getId();

                    List<CallRelation.CallInstance> instances = aggregatedCalls.computeIfAbsent(
                        aggregationKey, k -> new ArrayList<>());

                    instances.add(new CallRelation.CallInstance(
                        getElementLineNumber(element),
                        elementText.trim()
                    ));

                    debugLog("Found Python function call: " + callerFunction.getName() + " -> " + functionName + " at line " + getElementLineNumber(element));
                }
            }

        } catch (Exception e) {
            debugLog("Error analyzing Python function call element: " + e.getMessage());
        }
    }

    /**
     * 查找包含指定元素的Python函数
     */
    private AnalysisNode findContainingPythonFunction(PsiElement element, Map<String, AnalysisNode> functions) {
        PsiElement current = element.getParent();

        while (current != null) {
            if (isPythonFunctionDefinition(current)) {
                String functionName = extractPythonFunctionName(current);
                if (functionName != null && functions.containsKey(functionName)) {
                    return functions.get(functionName);
                }
            }
            current = current.getParent();
        }

        return null;
    }

    /**
     * 检查是否为Python内置函数
     */
    private boolean isPythonBuiltinFunction(String functionName) {
        String[] commonBuiltins = {
            "print", "len", "str", "int", "float", "bool", "list", "dict", "set", "tuple",
            "range", "enumerate", "zip", "map", "filter", "sorted", "reversed", "sum", "max", "min",
            "open", "input", "type", "isinstance", "hasattr", "getattr", "setattr", "delattr",
            "super", "property", "staticmethod", "classmethod", "abs", "round", "pow", "divmod"
        };

        for (String builtin : commonBuiltins) {
            if (builtin.equals(functionName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据ID查找节点
     */
    private AnalysisNode findNodeById(AnalysisResult result, String nodeId) {
        return result.getNodes().get(nodeId);
    }

    // ==================== 文本解析辅助方法 ====================

    /**
     * 从类定义行提取类名
     */
    private String extractClassName(String line) {
        try {
            // 匹配 "class ClassName:" 或 "class ClassName(BaseClass):"
            String trimmed = line.trim();
            if (trimmed.startsWith("class ")) {
                String afterClass = trimmed.substring(6).trim();
                int colonIndex = afterClass.indexOf(':');
                int parenIndex = afterClass.indexOf('(');

                int endIndex = afterClass.length();
                if (colonIndex > 0) endIndex = Math.min(endIndex, colonIndex);
                if (parenIndex > 0) endIndex = Math.min(endIndex, parenIndex);

                return afterClass.substring(0, endIndex).trim();
            }
        } catch (Exception e) {
            debugLog("Error extracting class name from: " + line);
        }
        return null;
    }

    /**
     * 从函数定义行提取函数名
     */
    private String extractFunctionName(String line) {
        try {
            // 匹配 "def function_name("
            String trimmed = line.trim();
            if (trimmed.startsWith("def ")) {
                String afterDef = trimmed.substring(4).trim();
                int parenIndex = afterDef.indexOf('(');
                if (parenIndex > 0) {
                    return afterDef.substring(0, parenIndex).trim();
                }
            }
        } catch (Exception e) {
            debugLog("Error extracting function name from: " + line);
        }
        return null;
    }

    /**
     * 从函数定义行提取参数列表
     */
    private String extractFunctionParameters(String line) {
        try {
            // 匹配 "def function_name(parameters):"
            String trimmed = line.trim();
            if (trimmed.startsWith("def ")) {
                int startParen = trimmed.indexOf('(');
                int endParen = trimmed.indexOf(')', startParen);
                if (startParen > 0 && endParen > startParen) {
                    return trimmed.substring(startParen + 1, endParen).trim();
                }
            }
        } catch (Exception e) {
            debugLog("Error extracting function parameters from: " + line);
        }
        return "";
    }

    /**
     * 从赋值语句提取变量名
     */
    private String extractVariableName(String line) {
        try {
            // 匹配 "variable_name = value"
            String trimmed = line.trim();
            int equalIndex = trimmed.indexOf('=');
            if (equalIndex > 0) {
                String varPart = trimmed.substring(0, equalIndex).trim();
                // 简单处理，只取第一个单词
                String[] parts = varPart.split("\\s+");
                if (parts.length > 0) {
                    return parts[0];
                }
            }
        } catch (Exception e) {
            debugLog("Error extracting variable name from: " + line);
        }
        return null;
    }

    // ==================== PSI工具方法 ====================

    /**
     * 获取PSI元素的行号
     */
    private int getElementLineNumber(PsiElement element) {
        try {
            PsiFile containingFile = element.getContainingFile();
            if (containingFile != null) {
                com.intellij.openapi.editor.Document document =
                    com.intellij.psi.PsiDocumentManager.getInstance(element.getProject())
                        .getDocument(containingFile);

                if (document != null) {
                    int offset = element.getTextOffset();
                    return document.getLineNumber(offset) + 1; // 转换为1基索引
                }
            }
        } catch (Exception e) {
            debugLog("Error getting line number for element: " + e.getMessage());
        }
        return 1; // 默认行号
    }

    // ==================== ID和签名生成方法 ====================

    private String generateClassId(String className, String moduleName) {
        return (moduleName != null ? moduleName + "." : "") + className;
    }

    private String generateFunctionId(String functionName, String className, String moduleName, String parameters) {
        StringBuilder id = new StringBuilder();
        if (moduleName != null) {
            id.append(moduleName).append(".");
        }
        if (className != null) {
            id.append(className).append(".");
        }
        id.append(functionName).append("(").append(parameters).append(")");
        return id.toString();
    }

    private String generateVariableId(String variableName, String moduleName) {
        return (moduleName != null ? moduleName + "." : "") + variableName;
    }

    private String generateClassSignature(String className, String moduleName) {
        return (moduleName != null ? moduleName + "." : "") + className;
    }

    private String generateFunctionSignature(String functionName, String className, String moduleName, String parameters) {
        StringBuilder signature = new StringBuilder();
        if (moduleName != null) {
            signature.append(moduleName).append(".");
        }
        if (className != null) {
            signature.append(className).append(".");
        }
        signature.append(functionName).append("(").append(parameters).append(")");
        return signature.toString();
    }

    private String generateVariableSignature(String variableName, String moduleName) {
        return (moduleName != null ? moduleName + "." : "") + variableName;
    }

    private void debugLog(String message) {
        if (DEBUG_MODE) {
            System.out.println("[PythonASTAnalyzer] " + message);
        }
    }
}