package com.sankuai.deepcode.astplugin.analyzer;

import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.psi.util.PsiTreeUtil;
import com.sankuai.deepcode.astplugin.model.AnalysisNode;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.CallRelation;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Python文件专用AST分析器
 * 
 * 特性：
 * - 支持Python函数、类、变量分析
 * - 支持Python模块识别
 * - 支持函数调用关系分析
 * - 线程安全的PSI访问
 * - 基于PSI接口的精确解析
 *
 * <AUTHOR>
 */
public class PythonASTAnalyzer {
    
    private static final boolean DEBUG_MODE = Boolean.getBoolean("ast.analyzer.debug");
    
    // 保留setup.py解析的正则表达式
    private static final Pattern SETUP_PY_NAME_PATTERN = Pattern.compile("name\\s*=\\s*['\"]([^'\"]+)['\"]");

    /**
     * 分析Python文件，返回完整的分析结果
     */
    public AnalysisResult analyze(PsiFile pythonFile) {
        return ReadAction.compute(() -> {
            AnalysisResult result = new AnalysisResult(pythonFile.getName(), "Python");
            
            try {
                debugLog("Starting Python analysis for file: " + pythonFile.getName());
                
                // 获取模块信息
                String moduleName = detectPythonModule(pythonFile);
                String filePath = getFilePath(pythonFile);
                
                // 使用PSI接口分析Python文件
                analyzePythonFile(pythonFile, result, moduleName, filePath);

                debugLog("Python analysis completed successfully. Found " + 
                        result.getNodes().size() + " nodes and " + 
                        result.getCallRelations().size() + " call relations");
                
            } catch (Exception e) {
                String errorMsg = "Python analysis failed for " + pythonFile.getName() + ": " + e.getMessage();
                result.addError(errorMsg);
                debugLog("ERROR: " + errorMsg);
                if (DEBUG_MODE) {
                    e.printStackTrace();
                }
            }
            
            return result;
        });
    }

    /**
     * 检测Python模块名 - 基于项目根目录的完整模块路径
     */
    private String detectPythonModule(PsiFile pythonFile) {
        try {
            VirtualFile virtualFile = pythonFile.getVirtualFile();
            if (virtualFile == null) {
                return pythonFile.getName().replace(".py", "");
            }

            Project project = pythonFile.getProject();
            VirtualFile projectRoot = project.getBaseDir();

            if (projectRoot == null) {
                return virtualFile.getNameWithoutExtension();
            }

            // 计算从项目根目录到文件的相对路径
            String relativePath = getRelativePathFromProject(virtualFile, projectRoot);
            if (relativePath == null) {
                return virtualFile.getNameWithoutExtension();
            }

            // 构建Python模块路径
            String modulePath = buildPythonModulePath(relativePath, virtualFile);
            debugLog("Detected Python module path: " + modulePath);
            return modulePath;

        } catch (Exception e) {
            debugLog("Error detecting Python module: " + e.getMessage());
            return pythonFile.getName().replace(".py", "");
        }
    }

    /**
     * 获取文件相对于项目根目录的路径
     */
    private String getRelativePathFromProject(VirtualFile file, VirtualFile projectRoot) {
        String filePath = file.getPath();
        String projectPath = projectRoot.getPath();

        if (filePath.startsWith(projectPath)) {
            return filePath.substring(projectPath.length() + 1);
        }
        return null;
    }

    /**
     * 构建Python模块路径
     */
    private String buildPythonModulePath(String relativePath, VirtualFile file) {
        // 移除文件扩展名
        String pathWithoutExtension = relativePath.replace(".py", "");

        // 将路径分隔符替换为点号
        String modulePath = pathWithoutExtension.replace("/", ".").replace("\\", ".");

        // 如果是__init__.py文件，移除最后的__init__
        if (modulePath.endsWith(".__init__")) {
            modulePath = modulePath.substring(0, modulePath.length() - 9);
        }

        // 如果模块路径为空，使用文件名
        if (modulePath.isEmpty()) {
            modulePath = file.getNameWithoutExtension();
        }

        return modulePath;
    }

    /**
     * 解析setup.py文件获取项目名
     */
    private String parseSetupPyName(com.intellij.openapi.vfs.VirtualFile setupFile) {
        try {
            String content = new String(setupFile.contentsToByteArray());
            Matcher matcher = SETUP_PY_NAME_PATTERN.matcher(content);
            if (matcher.find()) {
                return matcher.group(1);
            }
        } catch (Exception e) {
            debugLog("Error parsing setup.py: " + e.getMessage());
        }
        return null;
    }

    /**
     * 获取文件路径
     */
    private String getFilePath(PsiFile pythonFile) {
        try {
            com.intellij.openapi.vfs.VirtualFile virtualFile = pythonFile.getVirtualFile();
            if (virtualFile != null) {
                return virtualFile.getPath();
            }
        } catch (Exception e) {
            debugLog("Error getting file path: " + e.getMessage());
        }
        return pythonFile.getName();
    }

    /**
     * 使用增强的PSI接口分析Python文件
     */
    private void analyzePythonFile(PsiFile pythonFile, AnalysisResult result, String moduleName, String filePath) {
        try {
            // 存储所有函数节点，用于调用关系分析
            Map<String, AnalysisNode> functions = new HashMap<>();

            // 分析导入语句
            analyzePythonImports(pythonFile, result);

            // 分析类定义
            analyzePythonClasses(pythonFile, result, moduleName, filePath, functions);

            // 分析模块级函数
            analyzePythonModuleFunctions(pythonFile, result, moduleName, filePath, functions);

            // 分析模块级变量
            analyzePythonModuleVariables(pythonFile, result, moduleName, filePath);

            // 分析函数调用关系
            analyzePythonCallRelations(pythonFile, result, functions);

        } catch (Exception e) {
            debugLog("Error analyzing Python file with enhanced PSI: " + e.getMessage());
            result.addError("Enhanced PSI analysis error: " + e.getMessage());
        }
    }

    /**
     * 分析Python导入语句 - 使用增强的PSI分析
     */
    private void analyzePythonImports(PsiFile pythonFile, AnalysisResult result) {
        try {
            // 使用PsiTreeUtil查找所有可能的导入语句
            Collection<PsiElement> allElements = PsiTreeUtil.findChildrenOfType(pythonFile, PsiElement.class);
            int importCount = 0;

            for (PsiElement element : allElements) {
                if (isPythonImportStatement(element)) {
                    importCount++;
                    debugLog("Found Python import statement: " + element.getText().trim());
                }
            }

            if (importCount > 0) {
                result.updateStatistics("imports", importCount);
                debugLog("Found " + importCount + " Python import statements");
            }
        } catch (Exception e) {
            debugLog("Error analyzing Python imports: " + e.getMessage());
        }
    }

    /**
     * 分析Python类定义 - 使用增强的PSI分析
     */
    private void analyzePythonClasses(PsiFile pythonFile, AnalysisResult result, String moduleName, String filePath, Map<String, AnalysisNode> functions) {
        try {
            // 使用PsiTreeUtil查找所有可能的类定义
            Collection<PsiElement> allElements = PsiTreeUtil.findChildrenOfType(pythonFile, PsiElement.class);

            for (PsiElement element : allElements) {
                if (isPythonClassDefinition(element)) {
                    analyzePythonClassElement(element, result, moduleName, filePath, functions);
                }
            }
        } catch (Exception e) {
            debugLog("Error analyzing Python classes: " + e.getMessage());
        }
    }

    /**
     * 分析单个Python类元素
     */
    private void analyzePythonClassElement(PsiElement classElement, AnalysisResult result, String moduleName, String filePath, Map<String, AnalysisNode> functions) {
        try {
            // 使用增强的PSI方法提取类名
            String className = extractPythonClassName(classElement);
            if (className != null) {
                int lineNumber = getElementLineNumber(classElement);

                String classId = generateClassId(className, moduleName);
                AnalysisNode classNode = new AnalysisNode(
                        classId,
                        AnalysisNode.NodeType.CLASS,
                        className,
                        className,
                        moduleName != null ? moduleName : "",
                        lineNumber,
                        generateClassSignature(className, moduleName),
                        moduleName,
                        filePath,
                        "Python"
                );

                result.addNode(classNode);
                result.incrementStatistics("classes");
                debugLog("Added Python class: " + classId + " at line " + lineNumber);

                // 分析类中的方法
                analyzePythonClassMethods(classElement, className, result, moduleName, filePath, functions);
            }
        } catch (Exception e) {
            debugLog("Error analyzing Python class element: " + e.getMessage());
        }
    }

    /**
     * 分析Python类中的方法
     */
    private void analyzePythonClassMethods(PsiElement classElement, String className, AnalysisResult result, String moduleName, String filePath, Map<String, AnalysisNode> functions) {
        try {
            // 使用PsiTreeUtil查找类中的所有函数定义
            Collection<PsiElement> methodElements = PsiTreeUtil.findChildrenOfType(classElement, PsiElement.class);

            for (PsiElement element : methodElements) {
                if (isPythonFunctionDefinition(element)) {
                    String methodName = extractPythonFunctionName(element);
                    String parameters = extractPythonFunctionParameters(element);

                    if (methodName != null) {
                        int lineNumber = getElementLineNumber(element);

                        String methodId = generateFunctionId(methodName, className, moduleName, parameters);
                        AnalysisNode methodNode = new AnalysisNode(
                                methodId,
                                AnalysisNode.NodeType.FUNCTION,
                                methodName,
                                className,
                                moduleName != null ? moduleName : "",
                                lineNumber,
                                generateFunctionSignature(methodName, className, moduleName, parameters),
                                moduleName,
                                filePath,
                                "Python"
                        );

                        result.addNode(methodNode);

                        // 使用完整的方法ID作为key，避免重名冲突
                        String methodKey = className + "." + methodName;
                        functions.put(methodKey, methodNode);
                        functions.put(methodName, methodNode); // 保持向后兼容

                        result.incrementStatistics("functions");
                        debugLog("Added Python method: " + methodId + " at line " + lineNumber);

                        // 特殊处理__init__方法，分析其中的super()调用和父类__init__调用
                        if ("__init__".equals(methodName)) {
                            analyzeInitMethodCalls(element, methodNode, result, functions, className, moduleName);
                        }
                    }
                }
            }
        } catch (Exception e) {
            debugLog("Error analyzing Python class methods: " + e.getMessage());
        }
    }

    /**
     * 分析Python模块级函数
     */
    private void analyzePythonModuleFunctions(PsiFile pythonFile, AnalysisResult result, String moduleName, String filePath, Map<String, AnalysisNode> functions) {
        try {
            // 使用PsiTreeUtil查找所有顶级函数定义
            Collection<PsiElement> allElements = PsiTreeUtil.findChildrenOfType(pythonFile, PsiElement.class);

            for (PsiElement element : allElements) {
                if (isPythonFunctionDefinition(element) && isTopLevelElement(element, pythonFile)) {
                    String functionName = extractPythonFunctionName(element);
                    String parameters = extractPythonFunctionParameters(element);

                    if (functionName != null) {
                        int lineNumber = getElementLineNumber(element);

                        String functionId = generateFunctionId(functionName, null, moduleName, parameters);
                        AnalysisNode functionNode = new AnalysisNode(
                                functionId,
                                AnalysisNode.NodeType.FUNCTION,
                                functionName,
                                "",
                                moduleName != null ? moduleName : "",
                                lineNumber,
                                generateFunctionSignature(functionName, null, moduleName, parameters),
                                moduleName,
                                filePath,
                                "Python"
                        );

                        result.addNode(functionNode);
                        functions.put(functionName, functionNode);
                        result.incrementStatistics("functions");
                        debugLog("Added Python function: " + functionId + " at line " + lineNumber);
                    }
                }
            }
        } catch (Exception e) {
            debugLog("Error analyzing Python module functions: " + e.getMessage());
        }
    }

    /**
     * 分析Python模块级变量
     */
    private void analyzePythonModuleVariables(PsiFile pythonFile, AnalysisResult result, String moduleName, String filePath) {
        try {
            // 使用PsiTreeUtil查找所有顶级赋值语句
            Collection<PsiElement> allElements = PsiTreeUtil.findChildrenOfType(pythonFile, PsiElement.class);

            for (PsiElement element : allElements) {
                if (isPythonAssignmentStatement(element) && isTopLevelElement(element, pythonFile)) {
                    String variableName = extractPythonVariableName(element);

                    if (variableName != null && !isSpecialVariable(variableName)) {
                        int lineNumber = getElementLineNumber(element);

                        String variableId = generateVariableId(variableName, moduleName);
                        AnalysisNode variableNode = new AnalysisNode(
                                variableId,
                                AnalysisNode.NodeType.VARIABLE,
                                variableName,
                                "",
                                moduleName != null ? moduleName : "",
                                lineNumber,
                                generateVariableSignature(variableName, moduleName),
                                moduleName,
                                filePath,
                                "Python"
                        );

                        result.addNode(variableNode);
                        result.incrementStatistics("variables");
                        debugLog("Added Python variable: " + variableId + " at line " + lineNumber);
                    }
                }
            }
        } catch (Exception e) {
            debugLog("Error analyzing Python module variables: " + e.getMessage());
        }
    }

    /**
     * 分析__init__方法中的特殊调用（super()调用、父类__init__调用等）
     */
    private void analyzeInitMethodCalls(PsiElement initMethodElement, AnalysisNode initMethodNode,
                                      AnalysisResult result, Map<String, AnalysisNode> functions,
                                      String className, String moduleName) {
        try {
            debugLog("Analyzing __init__ method calls for class: " + className);

            // 分析super().__init__()调用
            analyzeSuperInitCalls(initMethodElement, initMethodNode, result, className, moduleName);

            // 分析直接的父类__init__调用
            analyzeParentInitCalls(initMethodElement, initMethodNode, result, functions, className, moduleName);

            // 分析其他类的__init__调用（组合模式）
            analyzeCompositionInitCalls(initMethodElement, initMethodNode, result, functions, className, moduleName);

        } catch (Exception e) {
            debugLog("Error analyzing __init__ method calls: " + e.getMessage());
        }
    }

    /**
     * 分析super().__init__()调用
     */
    private void analyzeSuperInitCalls(PsiElement initMethodElement, AnalysisNode initMethodNode,
                                     AnalysisResult result, String className, String moduleName) {
        try {
            String methodText = initMethodElement.getText();
            if (methodText == null) return;

            // 匹配super().__init__()调用的各种形式
            Pattern[] superPatterns = {
                Pattern.compile("super\\(\\)\\.__init__\\s*\\([^)]*\\)"),
                Pattern.compile("super\\([^)]*\\)\\.__init__\\s*\\([^)]*\\)"),
                Pattern.compile("super\\(\\s*" + className + "\\s*,\\s*self\\s*\\)\\.__init__\\s*\\([^)]*\\)")
            };

            for (Pattern pattern : superPatterns) {
                Matcher matcher = pattern.matcher(methodText);
                while (matcher.find()) {
                    String superCall = matcher.group();
                    int lineNumber = getLineNumberFromText(methodText, matcher.start());

                    // 创建super().__init__节点
                    String superInitId = "super." + className + ".__init__";
                    AnalysisNode superInitNode = new AnalysisNode(
                        superInitId,
                        AnalysisNode.NodeType.FUNCTION,
                        "__init__",
                        "super(" + className + ")",
                        moduleName != null ? moduleName : "",
                        lineNumber,
                        "super().__init__()",
                        moduleName,
                        "builtin",
                        "Python"
                    );

                    result.addNode(superInitNode);

                    // 创建调用关系
                    CallRelation superCallRelation = new CallRelation(
                        initMethodNode,
                        superInitNode,
                        lineNumber,
                        superCall,
                        true, // 外部调用
                        Arrays.asList(new CallRelation.CallInstance(lineNumber, superCall))
                    );

                    result.addCallRelation(superCallRelation);
                    result.incrementStatistics("function_calls");

                    debugLog("Found super().__init__ call in " + className + ".__init__ at line " + lineNumber);
                }
            }
        } catch (Exception e) {
            debugLog("Error analyzing super().__init__ calls: " + e.getMessage());
        }
    }

    /**
     * 分析直接的父类__init__调用
     */
    private void analyzeParentInitCalls(PsiElement initMethodElement, AnalysisNode initMethodNode,
                                      AnalysisResult result, Map<String, AnalysisNode> functions,
                                      String className, String moduleName) {
        try {
            String methodText = initMethodElement.getText();
            if (methodText == null) return;

            // 匹配ParentClass.__init__(self, ...)调用
            Pattern parentInitPattern = Pattern.compile("([A-Z][a-zA-Z0-9_]*)\\.__init__\\s*\\(\\s*self\\s*[,)]");
            Matcher matcher = parentInitPattern.matcher(methodText);

            while (matcher.find()) {
                String parentClassName = matcher.group(1);

                // 跳过自身类名
                if (parentClassName.equals(className)) {
                    continue;
                }

                String parentInitCall = matcher.group();
                int lineNumber = getLineNumberFromText(methodText, matcher.start());

                // 查找或创建父类__init__节点
                String parentInitKey = parentClassName + ".__init__";
                AnalysisNode parentInitNode = functions.get(parentInitKey);

                if (parentInitNode == null) {
                    // 创建外部父类__init__节点
                    String parentInitId = parentClassName + ".__init__";
                    parentInitNode = new AnalysisNode(
                        parentInitId,
                        AnalysisNode.NodeType.FUNCTION,
                        "__init__",
                        parentClassName,
                        moduleName != null ? moduleName : "",
                        0, // 外部方法没有行号
                        parentClassName + ".__init__()",
                        moduleName,
                        "external",
                        "Python"
                    );
                    result.addNode(parentInitNode);
                }

                // 创建调用关系
                CallRelation parentCallRelation = new CallRelation(
                    initMethodNode,
                    parentInitNode,
                    lineNumber,
                    parentInitCall,
                    true, // 外部调用
                    Arrays.asList(new CallRelation.CallInstance(lineNumber, parentInitCall))
                );

                result.addCallRelation(parentCallRelation);
                result.incrementStatistics("function_calls");

                debugLog("Found parent __init__ call: " + className + ".__init__ -> " + parentClassName + ".__init__ at line " + lineNumber);
            }
        } catch (Exception e) {
            debugLog("Error analyzing parent __init__ calls: " + e.getMessage());
        }
    }

    /**
     * 分析组合模式中的其他类__init__调用
     */
    private void analyzeCompositionInitCalls(PsiElement initMethodElement, AnalysisNode initMethodNode,
                                           AnalysisResult result, Map<String, AnalysisNode> functions,
                                           String className, String moduleName) {
        try {
            String methodText = initMethodElement.getText();
            if (methodText == null) return;

            // 匹配self.attribute = SomeClass(...)模式
            Pattern compositionPattern = Pattern.compile("self\\.[a-zA-Z_][a-zA-Z0-9_]*\\s*=\\s*([A-Z][a-zA-Z0-9_]*)\\s*\\(");
            Matcher matcher = compositionPattern.matcher(methodText);

            while (matcher.find()) {
                String composedClassName = matcher.group(1);

                // 跳过内置类型
                if (isPythonBuiltinType(composedClassName.toLowerCase())) {
                    continue;
                }

                String compositionCall = matcher.group();
                int lineNumber = getLineNumberFromText(methodText, matcher.start());

                // 查找或创建组合类的__init__节点
                String composedInitKey = composedClassName + ".__init__";
                AnalysisNode composedInitNode = functions.get(composedInitKey);

                if (composedInitNode == null) {
                    // 创建外部组合类__init__节点
                    String composedInitId = composedClassName + ".__init__";
                    composedInitNode = new AnalysisNode(
                        composedInitId,
                        AnalysisNode.NodeType.FUNCTION,
                        "__init__",
                        composedClassName,
                        moduleName != null ? moduleName : "",
                        0, // 外部方法没有行号
                        composedClassName + ".__init__()",
                        moduleName,
                        "external",
                        "Python"
                    );
                    result.addNode(composedInitNode);
                }

                // 创建调用关系
                CallRelation compositionCallRelation = new CallRelation(
                    initMethodNode,
                    composedInitNode,
                    lineNumber,
                    compositionCall,
                    true, // 外部调用
                    Arrays.asList(new CallRelation.CallInstance(lineNumber, compositionCall))
                );

                result.addCallRelation(compositionCallRelation);
                result.incrementStatistics("function_calls");

                debugLog("Found composition __init__ call: " + className + ".__init__ -> " + composedClassName + ".__init__ at line " + lineNumber);
            }
        } catch (Exception e) {
            debugLog("Error analyzing composition __init__ calls: " + e.getMessage());
        }
    }

    /**
     * 从文本中获取指定位置的行号
     */
    private int getLineNumberFromText(String text, int position) {
        try {
            String beforePosition = text.substring(0, Math.min(position, text.length()));
            return beforePosition.split("\n").length;
        } catch (Exception e) {
            return 1;
        }
    }

    /**
     * 分析Python函数调用关系
     */
    private void analyzePythonCallRelations(PsiFile pythonFile, AnalysisResult result, Map<String, AnalysisNode> functions) {
        debugLog("Starting Python call relation analysis");
        debugLog("Available functions: " + functions.keySet());

        try {
            Map<String, List<CallRelation.CallInstance>> aggregatedCalls = new HashMap<>();

            // 递归遍历所有PSI元素查找函数调用
            analyzePythonFunctionCallsRecursively(pythonFile, functions, aggregatedCalls);

            // 将聚合后的调用关系添加到结果中
            for (Map.Entry<String, List<CallRelation.CallInstance>> entry : aggregatedCalls.entrySet()) {
                String[] parts = entry.getKey().split(" -> ");
                if (parts.length == 2) {
                    String callerId = parts[0];
                    String calleeId = parts[1];

                    AnalysisNode caller = findNodeById(result, callerId);
                    AnalysisNode callee = findNodeById(result, calleeId);

                    if (caller != null && callee != null) {
                        List<CallRelation.CallInstance> instances = entry.getValue();
                        CallRelation.CallInstance firstInstance = instances.get(0);

                        CallRelation relation = new CallRelation(
                            caller, callee,
                            firstInstance.getLineNumber(),
                            firstInstance.getExpression(),
                            false, // 内部调用
                            instances
                        );

                        result.addCallRelation(relation);
                        result.incrementStatistics("function_calls");
                    }
                }
            }

            debugLog("Python call relation analysis completed. Found " + aggregatedCalls.size() + " unique relations");

        } catch (Exception e) {
            debugLog("Error analyzing Python call relations: " + e.getMessage());
        }
    }

    /**
     * 递归分析函数调用
     */
    private void analyzeFunctionCallsRecursively(PsiElement element, Map<String, AnalysisNode> functions,
                                               Map<String, List<CallRelation.CallInstance>> aggregatedCalls) {
        try {
            // 检查当前元素是否包含函数调用
            if (isPotentialFunctionCall(element)) {
                analyzeFunctionCallElement(element, functions, aggregatedCalls);
            }

            // 递归处理子元素
            PsiElement[] children = element.getChildren();
            for (PsiElement child : children) {
                analyzeFunctionCallsRecursively(child, functions, aggregatedCalls);
            }
        } catch (Exception e) {
            debugLog("Error in recursive function call analysis: " + e.getMessage());
        }
    }

    /**
     * 检查是否为潜在的函数调用
     */
    private boolean isPotentialFunctionCall(PsiElement element) {
        String elementText = element.getText();
        if (elementText == null) {
            return false;
        }

        // 检查是否包含函数调用模式
        return elementText.matches(".*\\w+\\s*\\(.*\\).*") &&
               !elementText.trim().startsWith("def ") &&
               !elementText.trim().startsWith("class ");
    }

    /**
     * 分析函数调用元素
     */
    private void analyzeFunctionCallElement(PsiElement element, Map<String, AnalysisNode> functions,
                                          Map<String, List<CallRelation.CallInstance>> aggregatedCalls) {
        try {
            // 查找调用者函数
            AnalysisNode callerFunction = findContainingFunction(element, functions);
            if (callerFunction == null) return;

            String elementText = element.getText();
            if (elementText == null) return;

            // 使用更精确的方式查找函数调用
            Pattern callPattern = Pattern.compile("(\\w+)\\s*\\(");
            Matcher matcher = callPattern.matcher(elementText);

            while (matcher.find()) {
                String functionName = matcher.group(1);

                // 检查是否为已知函数且不是调用者自身
                if (functions.containsKey(functionName) &&
                    !functionName.equals(callerFunction.getName()) &&
                    !isPythonBuiltinFunction(functionName)) {

                    AnalysisNode calleeFunction = functions.get(functionName);
                    String aggregationKey = callerFunction.getId() + " -> " + calleeFunction.getId();

                    List<CallRelation.CallInstance> instances = aggregatedCalls.computeIfAbsent(
                        aggregationKey, k -> new ArrayList<>());

                    instances.add(new CallRelation.CallInstance(
                        getElementLineNumber(element),
                        elementText.trim()
                    ));

                    debugLog("Found function call: " + callerFunction.getName() + " -> " + functionName + " at line " + getElementLineNumber(element));
                }
            }

        } catch (Exception e) {
            debugLog("Error analyzing function call element: " + e.getMessage());
        }
    }

    /**
     * 查找包含指定元素的函数
     */
    private AnalysisNode findContainingFunction(PsiElement element, Map<String, AnalysisNode> functions) {
        PsiElement current = element.getParent();

        while (current != null) {
            if (isFunctionDefinition(current)) {
                String functionName = extractFunctionNameFromPsi(current);
                if (functionName != null && functions.containsKey(functionName)) {
                    return functions.get(functionName);
                }
            }
            current = current.getParent();
        }

        return null;
    }



    /**
     * 检查是否为特殊变量（如__name__, __file__等）
     */
    private boolean isSpecialVariable(String variableName) {
        return variableName.startsWith("__") && variableName.endsWith("__") ||
               variableName.equals("self") || variableName.equals("cls");
    }

    // ==================== Python PSI识别方法 ====================

    /**
     * 检查PSI元素是否为Python导入语句 - 增强版本
     */
    private boolean isPythonImportStatement(PsiElement element) {
        if (element == null) return false;

        String text = element.getText();
        if (text == null) return false;

        String trimmed = text.trim();

        // 检查是否为导入语句的开始
        if (trimmed.startsWith("import ") || trimmed.startsWith("from ")) {
            // 确保是完整的导入语句，不是注释或字符串中的内容
            String[] lines = trimmed.split("\n");
            String firstLine = lines[0].trim();

            // 基本的导入语句模式检查
            if (firstLine.startsWith("import ")) {
                return firstLine.matches("import\\s+[a-zA-Z_][a-zA-Z0-9_]*(?:\\.[a-zA-Z_][a-zA-Z0-9_]*)*(?:\\s+as\\s+[a-zA-Z_][a-zA-Z0-9_]*)?(?:\\s*,\\s*[a-zA-Z_][a-zA-Z0-9_]*(?:\\.[a-zA-Z_][a-zA-Z0-9_]*)*(?:\\s+as\\s+[a-zA-Z_][a-zA-Z0-9_]*)?)*");
            } else if (firstLine.startsWith("from ")) {
                return firstLine.matches("from\\s+[a-zA-Z_][a-zA-Z0-9_]*(?:\\.[a-zA-Z_][a-zA-Z0-9_]*)*\\s+import\\s+(?:\\*|[a-zA-Z_][a-zA-Z0-9_]*(?:\\s+as\\s+[a-zA-Z_][a-zA-Z0-9_]*)?(?:\\s*,\\s*[a-zA-Z_][a-zA-Z0-9_]*(?:\\s+as\\s+[a-zA-Z_][a-zA-Z0-9_]*)?)*)");
            }
        }

        return false;
    }

    /**
     * 检查PSI元素是否为Python类定义 - 增强版本
     */
    private boolean isPythonClassDefinition(PsiElement element) {
        if (element == null) return false;

        String text = element.getText();
        if (text == null) return false;

        String trimmed = text.trim();
        String[] lines = trimmed.split("\n");
        String firstLine = lines[0].trim();

        // 检查类定义模式
        if (firstLine.startsWith("class ")) {
            // 更精确的类定义检查
            return firstLine.matches("class\\s+[a-zA-Z_][a-zA-Z0-9_]*(?:\\s*\\([^)]*\\))?\\s*:");
        }

        return false;
    }

    /**
     * 检查PSI元素是否为Python函数定义 - 增强版本
     */
    private boolean isPythonFunctionDefinition(PsiElement element) {
        if (element == null) return false;

        String text = element.getText();
        if (text == null) return false;

        String trimmed = text.trim();
        String[] lines = trimmed.split("\n");
        String firstLine = lines[0].trim();

        // 检查函数定义模式
        if (firstLine.startsWith("def ")) {
            // 更精确的函数定义检查，支持装饰器
            return firstLine.matches("def\\s+[a-zA-Z_][a-zA-Z0-9_]*\\s*\\([^)]*\\)\\s*(?:->\\s*[^:]+)?\\s*:");
        }

        return false;
    }

    /**
     * 检查PSI元素是否为Python赋值语句 - 增强版本
     */
    private boolean isPythonAssignmentStatement(PsiElement element) {
        if (element == null) return false;

        String text = element.getText();
        if (text == null) return false;

        String trimmed = text.trim();

        // 排除非赋值语句
        if (trimmed.startsWith("def ") || trimmed.startsWith("class ") ||
            trimmed.startsWith("import ") || trimmed.startsWith("from ") ||
            trimmed.startsWith("#") || trimmed.startsWith("\"\"\"") || trimmed.startsWith("'''")) {
            return false;
        }

        // 检查是否包含赋值操作符，但排除比较操作符
        if (trimmed.contains("=")) {
            // 更精确的赋值检查
            return trimmed.matches(".*[a-zA-Z_][a-zA-Z0-9_]*\\s*=\\s*.*") &&
                   !trimmed.contains("==") && !trimmed.contains("!=") &&
                   !trimmed.contains("<=") && !trimmed.contains(">=") &&
                   !trimmed.contains("+=") && !trimmed.contains("-=") &&
                   !trimmed.contains("*=") && !trimmed.contains("/=") &&
                   !trimmed.contains("//=") && !trimmed.contains("%=") &&
                   !trimmed.contains("**=") && !trimmed.contains("&=") &&
                   !trimmed.contains("|=") && !trimmed.contains("^=") &&
                   !trimmed.contains(">>=") && !trimmed.contains("<<=");
        }

        return false;
    }

    /**
     * 检查PSI元素是否为函数定义
     */
    private boolean isFunctionDefinition(PsiElement element) {
        if (element == null) return false;

        // 检查元素类型名称
        String elementType = element.getClass().getSimpleName();
        if (elementType.contains("PyFunction") || elementType.contains("Function")) {
            return true;
        }

        // 备用方案：检查文本内容
        String text = element.getText();
        if (text != null) {
            String firstLine = text.split("\n")[0].trim();
            return firstLine.startsWith("def ") && firstLine.contains("(") && firstLine.contains(":");
        }

        return false;
    }

    /**
     * 检查PSI元素是否为赋值语句（变量定义）
     */
    private boolean isAssignmentStatement(PsiElement element) {
        if (element == null) return false;

        // 检查元素类型名称
        String elementType = element.getClass().getSimpleName();
        if (elementType.contains("PyAssignment") || elementType.contains("Assignment")) {
            return true;
        }

        // 备用方案：检查文本内容
        String text = element.getText();
        if (text != null) {
            String trimmed = text.trim();
            return trimmed.contains("=") &&
                   !trimmed.startsWith("def ") &&
                   !trimmed.startsWith("class ") &&
                   !trimmed.startsWith("#");
        }

        return false;
    }

    /**
     * 检查PSI元素是否为导入语句
     */
    private boolean isImportStatement(PsiElement element) {
        if (element == null) return false;

        // 检查元素类型名称
        String elementType = element.getClass().getSimpleName();
        if (elementType.contains("PyImport") || elementType.contains("Import")) {
            return true;
        }

        // 备用方案：检查文本内容
        String text = element.getText();
        if (text != null) {
            String trimmed = text.trim();
            return trimmed.startsWith("import ") || trimmed.startsWith("from ");
        }

        return false;
    }

    // ==================== Python PSI元素信息提取方法 ====================

    /**
     * 从PSI元素中提取Python类名
     */
    private String extractPythonClassName(PsiElement classElement) {
        try {
            // 从文本中提取类名
            String text = classElement.getText();
            if (text != null) {
                String firstLine = text.split("\n")[0].trim();
                return extractClassName(firstLine);
            }
        } catch (Exception e) {
            debugLog("Error extracting Python class name from PSI: " + e.getMessage());
        }
        return null;
    }

    /**
     * 从PSI元素中提取Python函数名
     */
    private String extractPythonFunctionName(PsiElement functionElement) {
        try {
            // 从文本中提取函数名
            String text = functionElement.getText();
            if (text != null) {
                String firstLine = text.split("\n")[0].trim();
                return extractFunctionName(firstLine);
            }
        } catch (Exception e) {
            debugLog("Error extracting Python function name from PSI: " + e.getMessage());
        }
        return null;
    }

    /**
     * 从PSI元素中提取Python函数参数
     */
    private String extractPythonFunctionParameters(PsiElement functionElement) {
        try {
            // 从文本中提取参数
            String text = functionElement.getText();
            if (text != null) {
                String firstLine = text.split("\n")[0].trim();
                return extractFunctionParameters(firstLine);
            }
        } catch (Exception e) {
            debugLog("Error extracting Python function parameters from PSI: " + e.getMessage());
        }
        return "";
    }

    /**
     * 从PSI元素中提取Python变量名
     */
    private String extractPythonVariableName(PsiElement assignmentElement) {
        try {
            // 从文本中提取变量名
            String text = assignmentElement.getText();
            if (text != null) {
                return extractVariableName(text.trim());
            }
        } catch (Exception e) {
            debugLog("Error extracting Python variable name from PSI: " + e.getMessage());
        }
        return null;
    }

    /**
     * 从PSI元素中提取函数名
     */
    private String extractFunctionNameFromPsi(PsiElement functionElement) {
        try {
            // 从文本中提取函数名
            String text = functionElement.getText();
            if (text != null) {
                String firstLine = text.split("\n")[0].trim();
                return extractFunctionName(firstLine);
            }
        } catch (Exception e) {
            debugLog("Error extracting function name from PSI: " + e.getMessage());
        }
        return null;
    }

    /**
     * 从PSI元素中提取函数参数
     */
    private String extractFunctionParametersFromPsi(PsiElement functionElement) {
        try {
            // 从文本中提取参数
            String text = functionElement.getText();
            if (text != null) {
                String firstLine = text.split("\n")[0].trim();
                return extractFunctionParameters(firstLine);
            }
        } catch (Exception e) {
            debugLog("Error extracting function parameters from PSI: " + e.getMessage());
        }
        return "";
    }

    /**
     * 从PSI元素中提取变量名
     */
    private String extractVariableNameFromPsi(PsiElement assignmentElement) {
        try {
            // 从文本中提取变量名
            String text = assignmentElement.getText();
            if (text != null) {
                return extractVariableName(text.trim());
            }
        } catch (Exception e) {
            debugLog("Error extracting variable name from PSI: " + e.getMessage());
        }
        return null;
    }

    // ==================== Python PSI辅助方法 ====================

    /**
     * 检查元素是否为顶级元素（直接在文件根级别）
     */
    private boolean isTopLevelElement(PsiElement element, PsiFile file) {
        if (element == null || file == null) return false;

        // 检查父元素是否为文件本身或文件的直接子元素
        PsiElement parent = element.getParent();
        while (parent != null) {
            if (parent == file) {
                return true;
            }
            // 如果父元素是类或函数，则不是顶级元素
            if (isPythonClassDefinition(parent) || isPythonFunctionDefinition(parent)) {
                return false;
            }
            parent = parent.getParent();
        }
        return false;
    }

    /**
     * 递归分析Python函数调用
     */
    private void analyzePythonFunctionCallsRecursively(PsiElement element, Map<String, AnalysisNode> functions,
                                                     Map<String, List<CallRelation.CallInstance>> aggregatedCalls) {
        try {
            // 检查当前元素是否包含函数调用
            if (isPotentialPythonFunctionCall(element)) {
                analyzePythonFunctionCallElement(element, functions, aggregatedCalls);
            }

            // 递归处理子元素
            Collection<PsiElement> children = PsiTreeUtil.findChildrenOfType(element, PsiElement.class);
            for (PsiElement child : children) {
                if (child != element) { // 避免无限递归
                    analyzePythonFunctionCallsRecursively(child, functions, aggregatedCalls);
                }
            }
        } catch (Exception e) {
            debugLog("Error in recursive Python function call analysis: " + e.getMessage());
        }
    }

    /**
     * 检查是否为潜在的Python函数调用 - 增强版本
     */
    private boolean isPotentialPythonFunctionCall(PsiElement element) {
        String elementText = element.getText();
        if (elementText == null) {
            return false;
        }

        String trimmed = elementText.trim();

        // 排除定义语句和注释
        if (trimmed.startsWith("def ") || trimmed.startsWith("class ") ||
            trimmed.startsWith("#") || trimmed.startsWith("\"\"\"") || trimmed.startsWith("'''")) {
            return false;
        }

        // 检查是否包含函数调用模式
        // 支持简单函数调用、方法调用、链式调用等
        return trimmed.matches(".*[a-zA-Z_][a-zA-Z0-9_]*(?:\\.[a-zA-Z_][a-zA-Z0-9_]*)*\\s*\\([^)]*\\).*") &&
               !trimmed.contains("\n"); // 只检查单行调用
    }

    /**
     * 分析Python函数调用元素 - 增强版本，支持递归检测和方法调用
     */
    private void analyzePythonFunctionCallElement(PsiElement element, Map<String, AnalysisNode> functions,
                                                Map<String, List<CallRelation.CallInstance>> aggregatedCalls) {
        try {
            // 查找调用者函数
            AnalysisNode callerFunction = findContainingPythonFunction(element, functions);
            if (callerFunction == null) return;

            String elementText = element.getText();
            if (elementText == null) return;

            // 分析不同类型的函数调用
            analyzeFunctionCalls(elementText, callerFunction, functions, aggregatedCalls, element);
            analyzeMethodCalls(elementText, callerFunction, functions, aggregatedCalls, element);
            analyzeBuiltinMethodCalls(elementText, callerFunction, aggregatedCalls, element);

        } catch (Exception e) {
            debugLog("Error analyzing Python function call element: " + e.getMessage());
        }
    }

    /**
     * 分析普通函数调用
     */
    private void analyzeFunctionCalls(String elementText, AnalysisNode callerFunction,
                                    Map<String, AnalysisNode> functions,
                                    Map<String, List<CallRelation.CallInstance>> aggregatedCalls,
                                    PsiElement element) {
        // 匹配简单函数调用: function_name(...)
        Pattern callPattern = Pattern.compile("\\b([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(");
        Matcher matcher = callPattern.matcher(elementText);

        while (matcher.find()) {
            String functionName = matcher.group(1);

            // 跳过Python内置函数
            if (isPythonBuiltinFunction(functionName)) {
                continue;
            }

            if (functions.containsKey(functionName)) {
                AnalysisNode calleeFunction = functions.get(functionName);

                // 检查是否为递归调用
                if (functionName.equals(callerFunction.getName())) {
                    // 标记为递归调用，不重复添加
                    markAsRecursive(callerFunction, aggregatedCalls, element, elementText);
                } else {
                    // 普通函数调用
                    addCallRelation(callerFunction, calleeFunction, aggregatedCalls, element, elementText, false);
                }

                debugLog("Found function call: " + callerFunction.getName() + " -> " + functionName +
                        (functionName.equals(callerFunction.getName()) ? " (recursive)" : ""));
            }
        }
    }

    /**
     * 分析方法调用 (object.method())
     */
    private void analyzeMethodCalls(String elementText, AnalysisNode callerFunction,
                                  Map<String, AnalysisNode> functions,
                                  Map<String, List<CallRelation.CallInstance>> aggregatedCalls,
                                  PsiElement element) {
        // 匹配方法调用: object.method(...) 或 self.method(...)
        Pattern methodPattern = Pattern.compile("\\b(?:self|super\\(\\)|[a-zA-Z_][a-zA-Z0-9_]*)\\.([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(");
        Matcher matcher = methodPattern.matcher(elementText);

        while (matcher.find()) {
            String methodName = matcher.group(1);

            // 检查是否为类中定义的方法
            if (functions.containsKey(methodName)) {
                AnalysisNode calleeMethod = functions.get(methodName);

                // 检查是否为递归调用
                if (methodName.equals(callerFunction.getName())) {
                    markAsRecursive(callerFunction, aggregatedCalls, element, elementText);
                } else {
                    addCallRelation(callerFunction, calleeMethod, aggregatedCalls, element, elementText, false);
                }

                debugLog("Found method call: " + callerFunction.getName() + " -> " + methodName +
                        (methodName.equals(callerFunction.getName()) ? " (recursive)" : ""));
            }
        }
    }

    /**
     * 分析内置方法调用 (str.upper(), list.append() 等)
     */
    private void analyzeBuiltinMethodCalls(String elementText, AnalysisNode callerFunction,
                                         Map<String, List<CallRelation.CallInstance>> aggregatedCalls,
                                         PsiElement element) {

        // 1. 分析变量的方法调用 (variable.method())
        analyzeVariableMethodCalls(elementText, callerFunction, aggregatedCalls, element);

        // 2. 分析字符串方法调用
        analyzeStringMethodCalls(elementText, callerFunction, aggregatedCalls, element);

        // 3. 分析列表/字典/集合方法调用
        analyzeCollectionMethodCalls(elementText, callerFunction, aggregatedCalls, element);

        // 4. 特殊处理 super() 调用
        analyzeSuperMethodCalls(elementText, callerFunction, aggregatedCalls, element);
    }

    /**
     * 分析变量的方法调用
     */
    private void analyzeVariableMethodCalls(String elementText, AnalysisNode callerFunction,
                                          Map<String, List<CallRelation.CallInstance>> aggregatedCalls,
                                          PsiElement element) {
        // 匹配 variable.method() 模式
        Pattern variableMethodPattern = Pattern.compile("\\b([a-zA-Z_][a-zA-Z0-9_]*)\\.([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(");
        Matcher matcher = variableMethodPattern.matcher(elementText);

        while (matcher.find()) {
            String variableName = matcher.group(1);
            String methodName = matcher.group(2);

            // 跳过 self 和已知的内置类型
            if ("self".equals(variableName) || isPythonBuiltinType(variableName)) {
                continue;
            }

            String methodId = "method." + variableName + "." + methodName;
            AnalysisNode methodNode = createExternalMethodNode(methodName, methodId, variableName);

            addCallRelation(callerFunction, methodNode, aggregatedCalls, element, elementText, true);
            debugLog("Found variable method call: " + callerFunction.getName() + " -> " + variableName + "." + methodName);
        }
    }

    /**
     * 分析字符串方法调用
     */
    private void analyzeStringMethodCalls(String elementText, AnalysisNode callerFunction,
                                        Map<String, List<CallRelation.CallInstance>> aggregatedCalls,
                                        PsiElement element) {
        // 常见的字符串方法
        String[] stringMethods = {"upper", "lower", "strip", "split", "join", "replace", "find", "startswith", "endswith", "format"};

        for (String method : stringMethods) {
            Pattern pattern = Pattern.compile("\\." + method + "\\s*\\(");
            if (pattern.matcher(elementText).find()) {
                String methodId = "str." + method;
                AnalysisNode methodNode = createBuiltinMethodNode(method, methodId, "str");

                addCallRelation(callerFunction, methodNode, aggregatedCalls, element, elementText, true);
                debugLog("Found string method call: " + callerFunction.getName() + " -> str." + method);
            }
        }
    }

    /**
     * 分析集合类型方法调用
     */
    private void analyzeCollectionMethodCalls(String elementText, AnalysisNode callerFunction,
                                            Map<String, List<CallRelation.CallInstance>> aggregatedCalls,
                                            PsiElement element) {
        // 列表方法
        String[] listMethods = {"append", "extend", "insert", "remove", "pop", "clear", "index", "count", "sort", "reverse"};
        for (String method : listMethods) {
            Pattern pattern = Pattern.compile("\\." + method + "\\s*\\(");
            if (pattern.matcher(elementText).find()) {
                String methodId = "list." + method;
                AnalysisNode methodNode = createBuiltinMethodNode(method, methodId, "list");
                addCallRelation(callerFunction, methodNode, aggregatedCalls, element, elementText, true);
                debugLog("Found list method call: " + callerFunction.getName() + " -> list." + method);
            }
        }

        // 字典方法
        String[] dictMethods = {"get", "keys", "values", "items", "pop", "clear", "update", "setdefault"};
        for (String method : dictMethods) {
            Pattern pattern = Pattern.compile("\\." + method + "\\s*\\(");
            if (pattern.matcher(elementText).find()) {
                String methodId = "dict." + method;
                AnalysisNode methodNode = createBuiltinMethodNode(method, methodId, "dict");
                addCallRelation(callerFunction, methodNode, aggregatedCalls, element, elementText, true);
                debugLog("Found dict method call: " + callerFunction.getName() + " -> dict." + method);
            }
        }
    }

    /**
     * 分析 super() 方法调用 - 增强版本，支持多种super()调用模式
     */
    private void analyzeSuperMethodCalls(String elementText, AnalysisNode callerFunction,
                                       Map<String, List<CallRelation.CallInstance>> aggregatedCalls,
                                       PsiElement element) {
        try {
            // 分析各种super()调用模式
            analyzeSuperCallPatterns(elementText, callerFunction, aggregatedCalls, element);

        } catch (Exception e) {
            debugLog("Error analyzing super method calls: " + e.getMessage());
        }
    }

    /**
     * 分析各种super()调用模式
     */
    private void analyzeSuperCallPatterns(String elementText, AnalysisNode callerFunction,
                                        Map<String, List<CallRelation.CallInstance>> aggregatedCalls,
                                        PsiElement element) {
        // 模式1: super().method_name()
        Pattern superPattern1 = Pattern.compile("super\\(\\)\\.([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(");
        analyzeSuperPattern(elementText, callerFunction, aggregatedCalls, element, superPattern1, "super()");

        // 模式2: super(ClassName, self).method_name()
        Pattern superPattern2 = Pattern.compile("super\\(\\s*([A-Z][a-zA-Z0-9_]*)\\s*,\\s*self\\s*\\)\\.([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(");
        Matcher matcher2 = superPattern2.matcher(elementText);
        while (matcher2.find()) {
            String className = matcher2.group(1);
            String methodName = matcher2.group(2);
            String superMethodId = "super(" + className + ")." + methodName;
            AnalysisNode superMethod = createSuperMethodNode(methodName, superMethodId, className);
            addCallRelation(callerFunction, superMethod, aggregatedCalls, element, elementText, true);
            debugLog("Found super(" + className + ") method call: " + callerFunction.getName() + " -> " + superMethodId);
        }

        // 模式3: super(type(self), self).method_name()
        Pattern superPattern3 = Pattern.compile("super\\(\\s*type\\(self\\)\\s*,\\s*self\\s*\\)\\.([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(");
        analyzeSuperPattern(elementText, callerFunction, aggregatedCalls, element, superPattern3, "super(type(self), self)");

        // 模式4: 直接的父类方法调用 ParentClass.method_name(self, ...)
        Pattern parentMethodPattern = Pattern.compile("([A-Z][a-zA-Z0-9_]*)\\.([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(\\s*self\\s*[,)]");
        Matcher parentMatcher = parentMethodPattern.matcher(elementText);
        while (parentMatcher.find()) {
            String parentClassName = parentMatcher.group(1);
            String methodName = parentMatcher.group(2);

            // 跳过当前类的方法调用
            String currentClassName = extractClassNameFromFunction(callerFunction);
            if (!parentClassName.equals(currentClassName)) {
                String parentMethodId = parentClassName + "." + methodName;
                AnalysisNode parentMethod = createParentMethodNode(methodName, parentMethodId, parentClassName);
                addCallRelation(callerFunction, parentMethod, aggregatedCalls, element, elementText, true);
                debugLog("Found parent class method call: " + callerFunction.getName() + " -> " + parentMethodId);
            }
        }
    }

    /**
     * 分析特定的super()调用模式
     */
    private void analyzeSuperPattern(String elementText, AnalysisNode callerFunction,
                                   Map<String, List<CallRelation.CallInstance>> aggregatedCalls,
                                   PsiElement element, Pattern pattern, String superType) {
        Matcher matcher = pattern.matcher(elementText);
        while (matcher.find()) {
            String methodName = matcher.group(matcher.groupCount()); // 获取最后一个捕获组（方法名）
            String superMethodId = superType + "." + methodName;
            AnalysisNode superMethod = createSuperMethodNode(methodName, superMethodId, superType);
            addCallRelation(callerFunction, superMethod, aggregatedCalls, element, elementText, true);
            debugLog("Found " + superType + " method call: " + callerFunction.getName() + " -> " + superMethodId);
        }
    }

    /**
     * 创建super()方法节点
     */
    private AnalysisNode createSuperMethodNode(String methodName, String methodId, String superType) {
        return new AnalysisNode(
            methodId,
            AnalysisNode.NodeType.FUNCTION,
            methodName,
            superType,
            superType,
            0, // super方法没有行号
            superType + "." + methodName + "()",
            superType,
            "builtin",
            "Python"
        );
    }

    /**
     * 创建父类方法节点
     */
    private AnalysisNode createParentMethodNode(String methodName, String methodId, String parentClassName) {
        return new AnalysisNode(
            methodId,
            AnalysisNode.NodeType.FUNCTION,
            methodName,
            parentClassName,
            parentClassName,
            0, // 父类方法没有行号
            parentClassName + "." + methodName + "()",
            parentClassName,
            "external",
            "Python"
        );
    }

    /**
     * 从函数节点中提取类名
     */
    private String extractClassNameFromFunction(AnalysisNode functionNode) {
        try {
            String className = functionNode.getClassName();
            if (className != null && !className.isEmpty()) {
                return className;
            }

            // 从函数ID中提取类名
            String functionId = functionNode.getId();
            if (functionId.contains(".")) {
                String[] parts = functionId.split("\\.");
                if (parts.length >= 2) {
                    return parts[parts.length - 2]; // 倒数第二个部分通常是类名
                }
            }
        } catch (Exception e) {
            debugLog("Error extracting class name from function: " + e.getMessage());
        }
        return "";
    }

    /**
     * 标记函数为递归调用
     */
    private void markAsRecursive(AnalysisNode callerFunction,
                               Map<String, List<CallRelation.CallInstance>> aggregatedCalls,
                               PsiElement element, String elementText) {
        String recursiveKey = callerFunction.getId() + " -> " + callerFunction.getId() + " (recursive)";

        // 检查是否已经标记为递归
        if (!aggregatedCalls.containsKey(recursiveKey)) {
            List<CallRelation.CallInstance> instances = new ArrayList<>();
            instances.add(new CallRelation.CallInstance(
                getElementLineNumber(element),
                elementText.trim() + " // recursive call"
            ));
            aggregatedCalls.put(recursiveKey, instances);
        }
    }

    /**
     * 添加调用关系
     */
    private void addCallRelation(AnalysisNode caller, AnalysisNode callee,
                               Map<String, List<CallRelation.CallInstance>> aggregatedCalls,
                               PsiElement element, String elementText, boolean isExternal) {
        String aggregationKey = caller.getId() + " -> " + callee.getId();

        List<CallRelation.CallInstance> instances = aggregatedCalls.computeIfAbsent(
            aggregationKey, k -> new ArrayList<>());

        instances.add(new CallRelation.CallInstance(
            getElementLineNumber(element),
            elementText.trim() + (isExternal ? " // external call" : "")
        ));
    }

    /**
     * 创建内置方法节点
     */
    private AnalysisNode createBuiltinMethodNode(String methodName, String methodId) {
        return new AnalysisNode(
            methodId,
            AnalysisNode.NodeType.FUNCTION,
            methodName,
            "builtin",
            "builtin",
            0, // 内置方法没有行号
            "builtin." + methodName + "()",
            "builtin",
            "builtin",
            "Python"
        );
    }

    /**
     * 创建内置方法节点（带类型）
     */
    private AnalysisNode createBuiltinMethodNode(String methodName, String methodId, String typeName) {
        return new AnalysisNode(
            methodId,
            AnalysisNode.NodeType.FUNCTION,
            methodName,
            typeName,
            typeName,
            0, // 内置方法没有行号
            typeName + "." + methodName + "()",
            typeName,
            "builtin",
            "Python"
        );
    }

    /**
     * 创建外部方法节点
     */
    private AnalysisNode createExternalMethodNode(String methodName, String methodId, String objectName) {
        return new AnalysisNode(
            methodId,
            AnalysisNode.NodeType.FUNCTION,
            methodName,
            objectName,
            objectName,
            0, // 外部方法没有行号
            objectName + "." + methodName + "()",
            objectName,
            "external",
            "Python"
        );
    }

    /**
     * 检查是否为Python内置类型
     */
    private boolean isPythonBuiltinType(String typeName) {
        String[] builtinTypes = {
            "str", "int", "float", "bool", "list", "dict", "set", "tuple",
            "bytes", "bytearray", "frozenset", "complex", "range", "enumerate",
            "zip", "map", "filter", "reversed", "sorted"
        };

        for (String builtin : builtinTypes) {
            if (builtin.equals(typeName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 查找包含指定元素的Python函数
     */
    private AnalysisNode findContainingPythonFunction(PsiElement element, Map<String, AnalysisNode> functions) {
        PsiElement current = element.getParent();

        while (current != null) {
            if (isPythonFunctionDefinition(current)) {
                String functionName = extractPythonFunctionName(current);
                if (functionName != null && functions.containsKey(functionName)) {
                    return functions.get(functionName);
                }
            }
            current = current.getParent();
        }

        return null;
    }

    /**
     * 检查是否为Python内置函数
     */
    private boolean isPythonBuiltinFunction(String functionName) {
        String[] commonBuiltins = {
            "print", "len", "str", "int", "float", "bool", "list", "dict", "set", "tuple",
            "range", "enumerate", "zip", "map", "filter", "sorted", "reversed", "sum", "max", "min",
            "open", "input", "type", "isinstance", "hasattr", "getattr", "setattr", "delattr",
            "super", "property", "staticmethod", "classmethod", "abs", "round", "pow", "divmod"
        };

        for (String builtin : commonBuiltins) {
            if (builtin.equals(functionName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据ID查找节点
     */
    private AnalysisNode findNodeById(AnalysisResult result, String nodeId) {
        return result.getNodes().get(nodeId);
    }

    // ==================== 文本解析辅助方法 ====================

    /**
     * 从类定义行提取类名
     */
    private String extractClassName(String line) {
        try {
            // 匹配 "class ClassName:" 或 "class ClassName(BaseClass):"
            String trimmed = line.trim();
            if (trimmed.startsWith("class ")) {
                String afterClass = trimmed.substring(6).trim();
                int colonIndex = afterClass.indexOf(':');
                int parenIndex = afterClass.indexOf('(');

                int endIndex = afterClass.length();
                if (colonIndex > 0) endIndex = Math.min(endIndex, colonIndex);
                if (parenIndex > 0) endIndex = Math.min(endIndex, parenIndex);

                return afterClass.substring(0, endIndex).trim();
            }
        } catch (Exception e) {
            debugLog("Error extracting class name from: " + line);
        }
        return null;
    }

    /**
     * 从函数定义行提取函数名
     */
    private String extractFunctionName(String line) {
        try {
            // 匹配 "def function_name("
            String trimmed = line.trim();
            if (trimmed.startsWith("def ")) {
                String afterDef = trimmed.substring(4).trim();
                int parenIndex = afterDef.indexOf('(');
                if (parenIndex > 0) {
                    return afterDef.substring(0, parenIndex).trim();
                }
            }
        } catch (Exception e) {
            debugLog("Error extracting function name from: " + line);
        }
        return null;
    }

    /**
     * 从函数定义行提取参数列表
     */
    private String extractFunctionParameters(String line) {
        try {
            // 匹配 "def function_name(parameters):"
            String trimmed = line.trim();
            if (trimmed.startsWith("def ")) {
                int startParen = trimmed.indexOf('(');
                int endParen = trimmed.indexOf(')', startParen);
                if (startParen > 0 && endParen > startParen) {
                    return trimmed.substring(startParen + 1, endParen).trim();
                }
            }
        } catch (Exception e) {
            debugLog("Error extracting function parameters from: " + line);
        }
        return "";
    }

    /**
     * 从赋值语句提取变量名
     */
    private String extractVariableName(String line) {
        try {
            // 匹配 "variable_name = value"
            String trimmed = line.trim();
            int equalIndex = trimmed.indexOf('=');
            if (equalIndex > 0) {
                String varPart = trimmed.substring(0, equalIndex).trim();
                // 简单处理，只取第一个单词
                String[] parts = varPart.split("\\s+");
                if (parts.length > 0) {
                    return parts[0];
                }
            }
        } catch (Exception e) {
            debugLog("Error extracting variable name from: " + line);
        }
        return null;
    }

    // ==================== PSI工具方法 ====================

    /**
     * 获取PSI元素的行号
     */
    private int getElementLineNumber(PsiElement element) {
        try {
            PsiFile containingFile = element.getContainingFile();
            if (containingFile != null) {
                com.intellij.openapi.editor.Document document =
                    com.intellij.psi.PsiDocumentManager.getInstance(element.getProject())
                        .getDocument(containingFile);

                if (document != null) {
                    int offset = element.getTextOffset();
                    return document.getLineNumber(offset) + 1; // 转换为1基索引
                }
            }
        } catch (Exception e) {
            debugLog("Error getting line number for element: " + e.getMessage());
        }
        return 1; // 默认行号
    }

    // ==================== ID和签名生成方法 ====================

    private String generateClassId(String className, String moduleName) {
        return (moduleName != null ? moduleName + "." : "") + className;
    }

    private String generateFunctionId(String functionName, String className, String moduleName, String parameters) {
        StringBuilder id = new StringBuilder();
        if (moduleName != null) {
            id.append(moduleName).append(".");
        }
        if (className != null) {
            id.append(className).append(".");
        }
        id.append(functionName).append("(").append(parameters).append(")");
        return id.toString();
    }

    private String generateVariableId(String variableName, String moduleName) {
        return (moduleName != null ? moduleName + "." : "") + variableName;
    }

    private String generateClassSignature(String className, String moduleName) {
        return (moduleName != null ? moduleName + "." : "") + className;
    }

    private String generateFunctionSignature(String functionName, String className, String moduleName, String parameters) {
        StringBuilder signature = new StringBuilder();
        if (moduleName != null) {
            signature.append(moduleName).append(".");
        }
        if (className != null) {
            signature.append(className).append(".");
        }
        signature.append(functionName).append("(").append(parameters).append(")");
        return signature.toString();
    }

    private String generateVariableSignature(String variableName, String moduleName) {
        return (moduleName != null ? moduleName + "." : "") + variableName;
    }

    private void debugLog(String message) {
        if (DEBUG_MODE) {
            System.out.println("[PythonASTAnalyzer] " + message);
        }
    }
}