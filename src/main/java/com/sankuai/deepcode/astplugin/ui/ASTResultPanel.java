package com.sankuai.deepcode.astplugin.ui;

import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBPanel;
import com.intellij.ui.components.JBScrollPane;
import com.intellij.ui.components.JBTabbedPane;
import com.intellij.ui.treeStructure.Tree;
import com.sankuai.deepcode.astplugin.model.AnalysisNode;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.CallRelation;

import javax.swing.*;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeModel;
import java.awt.*;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.*;

/**
 * AST分析结果显示面板
 */
public class ASTResultPanel extends JBPanel<ASTResultPanel> {
    private JBTabbedPane tabbedPane;
    private JBLabel headerLabel;
    private Tree structureTree;
    private JPanel callRelationsPanel;
    private JPanel statisticsPanel;
    
    public ASTResultPanel() {
        super(new BorderLayout());
        initializeUI();
    }
    
    private void initializeUI() {
        // 头部信息
        headerLabel = new JBLabel("暂无分析结果");
        headerLabel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        headerLabel.setFont(headerLabel.getFont().deriveFont(Font.BOLD, 14f));
        add(headerLabel, BorderLayout.NORTH);
        
        // 选项卡面板
        tabbedPane = new JBTabbedPane();
        
        // 代码结构选项卡
        structureTree = new Tree();
        structureTree.setRootVisible(false);
        JBScrollPane structureScrollPane = new JBScrollPane(structureTree);
        tabbedPane.addTab("代码结构", structureScrollPane);
        
        // 调用关系选项卡
        callRelationsPanel = new JBPanel<>(new BorderLayout());
        tabbedPane.addTab("调用关系", callRelationsPanel);
        
        // 统计信息选项卡
        statisticsPanel = new JBPanel<>(new BorderLayout());
        tabbedPane.addTab("统计信息", statisticsPanel);
        
        add(tabbedPane, BorderLayout.CENTER);
        
        // 初始状态
        clearResults();
    }
    
    public void displayResult(AnalysisResult result) {
        if (result == null) {
            clearResults();
            return;
        }
        
        // 更新头部信息
        updateHeader(result);
        
        // 更新代码结构树
        updateStructureTree(result);
        
        // 更新调用关系面板
        updateCallRelationsPanel(result);
        
        // 更新统计信息面板
        updateStatisticsPanel(result);
        
        tabbedPane.setVisible(true);
    }
    
    public void clearResults() {
        headerLabel.setText("暂无分析结果");
        structureTree.setModel(new DefaultTreeModel(new DefaultMutableTreeNode("暂无数据")));
        callRelationsPanel.removeAll();
        statisticsPanel.removeAll();
        tabbedPane.setVisible(false);
        repaint();
    }
    
    private void updateHeader(AnalysisResult result) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String headerText = String.format(
            "<html><b>文件:</b> %s &nbsp;&nbsp; <b>语言:</b> %s &nbsp;&nbsp; <b>分析时间:</b> %s</html>",
            result.getFileName(),
            result.getLanguage(),
            sdf.format(result.getTimestamp())
        );
        headerLabel.setText(headerText);
    }
    
    private void updateStructureTree(AnalysisResult result) {
        DefaultMutableTreeNode root = new DefaultMutableTreeNode("代码结构");
        
        // 按类型分组节点
        Map<AnalysisNode.NodeType, List<AnalysisNode>> nodesByType = new HashMap<>();
        for (AnalysisNode node : result.getNodes().values()) {
            nodesByType.computeIfAbsent(node.getType(), k -> new ArrayList<>()).add(node);
        }
        
        // 为每种类型创建树节点
        for (AnalysisNode.NodeType type : AnalysisNode.NodeType.values()) {
            List<AnalysisNode> typeNodes = nodesByType.get(type);
            if (typeNodes != null && !typeNodes.isEmpty()) {
                DefaultMutableTreeNode typeNode = new DefaultMutableTreeNode(
                    String.format("%s (%d)", getTypeDisplayName(type), typeNodes.size())
                );
                
                typeNodes.stream()
                    .sorted(Comparator.comparing(AnalysisNode::getName))
                    .forEach(node -> {
                        String nodeText = String.format("%s (第%d行)", node.getName(), node.getLineNumber());
                        DefaultMutableTreeNode nodeTreeNode = new DefaultMutableTreeNode(nodeText);
                        typeNode.add(nodeTreeNode);
                    });
                
                root.add(typeNode);
            }
        }
        
        structureTree.setModel(new DefaultTreeModel(root));
        
        // 展开所有节点
        for (int i = 0; i < structureTree.getRowCount(); i++) {
            structureTree.expandRow(i);
        }
    }
    
    private void updateCallRelationsPanel(AnalysisResult result) {
        callRelationsPanel.removeAll();
        
        List<CallRelation> relations = result.getCallRelations();
        if (relations.isEmpty()) {
            JBLabel noDataLabel = new JBLabel("暂无调用关系数据", SwingConstants.CENTER);
            callRelationsPanel.add(noDataLabel, BorderLayout.CENTER);
        } else {
            JPanel contentPanel = new JBPanel<>(new BorderLayout());
            
            // 按调用方分组聚合调用关系
            Map<String, List<CallRelation>> relationsByCaller = new LinkedHashMap<>();
            for (CallRelation relation : relations) {
                String callerSignature = relation.getCaller().getSignature();
                relationsByCaller.computeIfAbsent(callerSignature, k -> new ArrayList<>()).add(relation);
            }

            // 创建分组显示的树结构
            DefaultMutableTreeNode root = new DefaultMutableTreeNode("调用关系");

            for (Map.Entry<String, List<CallRelation>> entry : relationsByCaller.entrySet()) {
                String callerSignature = entry.getKey();
                List<CallRelation> callerRelations = entry.getValue();

                // 创建调用方节点
                String callerNodeText = String.format("📞 %s (%d个调用)",
                    getSimpleMethodName(callerSignature), callerRelations.size());
                DefaultMutableTreeNode callerNode = new DefaultMutableTreeNode(callerNodeText);

                // 为调用方节点设置完整信息（用于tooltip）
                CallerNodeInfo callerInfo = new CallerNodeInfo(
                    callerRelations.get(0).getCaller(),
                    callerRelations.size()
                );
                callerNode.setUserObject(callerInfo);

                // 添加被调用方法节点
                for (CallRelation relation : callerRelations) {
                    String calleeText = String.format("→ %s%s",
                        getSimpleMethodName(relation.getCallee().getSignature()),
                        relation.isExternal() ? " [外部]" : "");

                    DefaultMutableTreeNode calleeNode = new DefaultMutableTreeNode(calleeText);

                    // 为被调用方节点设置详细信息
                    CalleeNodeInfo calleeInfo = new CalleeNodeInfo(relation);
                    calleeNode.setUserObject(calleeInfo);

                    callerNode.add(calleeNode);
                }

                root.add(callerNode);
            }

            // 创建自定义的树组件
            Tree callRelationTree = new Tree(new DefaultTreeModel(root));
            callRelationTree.setRootVisible(false);
            callRelationTree.setShowsRootHandles(true);

            // 设置自定义的单元格渲染器
            callRelationTree.setCellRenderer(new CallRelationTreeCellRenderer());

            // 添加鼠标监听器来显示tooltip
            callRelationTree.addMouseMotionListener(new java.awt.event.MouseMotionAdapter() {
                @Override
                public void mouseMoved(java.awt.event.MouseEvent e) {
                    int row = callRelationTree.getRowForLocation(e.getX(), e.getY());
                    if (row >= 0) {
                        javax.swing.tree.TreePath path = callRelationTree.getPathForRow(row);
                        if (path != null) {
                            DefaultMutableTreeNode node = (DefaultMutableTreeNode) path.getLastPathComponent();
                            Object userObject = node.getUserObject();

                            String tooltip = null;
                            if (userObject instanceof CallerNodeInfo) {
                                CallerNodeInfo info = (CallerNodeInfo) userObject;
                                tooltip = String.format(
                                    "<html><b>调用方:</b> %s<br/>" +
                                    "<b>ID:</b> %s<br/>" +
                                    "<b>定义行号:</b> %d<br/>" +
                                    "<b>调用数量:</b> %d个不同方法</html>",
                                    info.caller.getSignature(),
                                    info.caller.getId(),
                                    info.caller.getLineNumber(),
                                    info.callCount
                                );
                            } else if (userObject instanceof CalleeNodeInfo) {
                                CalleeNodeInfo info = (CalleeNodeInfo) userObject;
                                StringBuilder callInstances = new StringBuilder();
                                for (CallRelation.CallInstance instance : info.relation.getAllCallInstances()) {
                                    if (callInstances.length() > 0) callInstances.append("<br/>");
                                    callInstances.append("第").append(instance.getLineNumber())
                                               .append("行: ").append(instance.getExpression());
                                }

                                tooltip = String.format(
                                    "<html><b>被调用方:</b> %s<br/>" +
                                    "<b>ID:</b> %s<br/>" +
                                    "<b>类型:</b> %s<br/>" +
                                    "<b>调用次数:</b> %d次<br/>" +
                                    "<b>调用位置:</b><br/>%s</html>",
                                    info.relation.getCallee().getSignature(),
                                    info.relation.getCallee().getId(),
                                    info.relation.isExternal() ? "外部调用" : "内部调用",
                                    info.relation.getCallCount(),
                                    callInstances.toString()
                                );
                            }

                            callRelationTree.setToolTipText(tooltip);
                        }
                    } else {
                        callRelationTree.setToolTipText(null);
                    }
                }
            });

            // 展开所有调用方节点
            for (int i = 0; i < callRelationTree.getRowCount(); i++) {
                callRelationTree.expandRow(i);
            }

            contentPanel.add(new JBScrollPane(callRelationTree), BorderLayout.CENTER);

            // 统计信息
            JPanel statsPanel = new JBPanel<>(new FlowLayout(FlowLayout.LEFT));
            statsPanel.add(new JBLabel("总调用关系: " + relations.size()));
            statsPanel.add(new JBLabel("  调用方数量: " + relationsByCaller.size()));

            long externalCalls = relations.stream().mapToLong(r -> r.isExternal() ? 1 : 0).sum();
            long internalCalls = relations.size() - externalCalls;
            statsPanel.add(new JBLabel("  内部调用: " + internalCalls));
            statsPanel.add(new JBLabel("  外部调用: " + externalCalls));

            contentPanel.add(statsPanel, BorderLayout.SOUTH);
            callRelationsPanel.add(contentPanel, BorderLayout.CENTER);
        }
        
        callRelationsPanel.revalidate();
        callRelationsPanel.repaint();
    }
    
    private void updateStatisticsPanel(AnalysisResult result) {
        statisticsPanel.removeAll();
        
        Map<String, Integer> statistics = result.getStatistics();
        if (statistics.isEmpty()) {
            JBLabel noDataLabel = new JBLabel("暂无统计数据", SwingConstants.CENTER);
            statisticsPanel.add(noDataLabel, BorderLayout.CENTER);
        } else {
            JPanel contentPanel = new JBPanel<>(new GridBagLayout());
            GridBagConstraints gbc = new GridBagConstraints();
            gbc.insets = new Insets(5, 10, 5, 10);
            gbc.anchor = GridBagConstraints.WEST;
            
            int row = 0;
            for (Map.Entry<String, Integer> entry : statistics.entrySet()) {
                gbc.gridx = 0;
                gbc.gridy = row;
                contentPanel.add(new JBLabel(getStatisticDisplayName(entry.getKey()) + ":"), gbc);
                
                gbc.gridx = 1;
                JBLabel valueLabel = new JBLabel(entry.getValue().toString());
                valueLabel.setFont(valueLabel.getFont().deriveFont(Font.BOLD));
                contentPanel.add(valueLabel, gbc);
                
                row++;
            }
            
            statisticsPanel.add(contentPanel, BorderLayout.NORTH);
        }
        
        statisticsPanel.revalidate();
        statisticsPanel.repaint();
    }
    
    private String getTypeDisplayName(AnalysisNode.NodeType type) {
        switch (type) {
            case CLASS: return "类";
            case METHOD: return "方法";
            case FIELD: return "字段";
            case INTERFACE: return "接口";
            case ENUM: return "枚举";
            default: return type.name();
        }
    }
    
    private String getStatisticDisplayName(String key) {
        switch (key) {
            case "file_size": return "文件大小";
            case "total_elements": return "总元素数";
            case "class_count": return "类数量";
            case "method_count": return "方法数量";
            case "field_count": return "字段数量";
            case "interface_count": return "接口数量";
            case "enum_count": return "枚举数量";
            default: return key;
        }
    }

    /**
     * 从完整的方法签名中提取简单的方法名
     */
    private String getSimpleMethodName(String signature) {
        if (signature == null) return "Unknown";

        // 提取类名.方法名部分
        int lastDotIndex = signature.lastIndexOf('.');
        if (lastDotIndex > 0) {
            String classAndMethod = signature.substring(0, lastDotIndex);
            int secondLastDotIndex = classAndMethod.lastIndexOf('.');
            if (secondLastDotIndex > 0) {
                return classAndMethod.substring(secondLastDotIndex + 1);
            }
        }

        return signature;
    }

    /**
     * 调用方节点信息
     */
    private static class CallerNodeInfo {
        final AnalysisNode caller;
        final int callCount;

        CallerNodeInfo(AnalysisNode caller, int callCount) {
            this.caller = caller;
            this.callCount = callCount;
        }

        @Override
        public String toString() {
            return String.format("📞 %s (%d个调用)",
                caller.getName(), callCount);
        }
    }

    /**
     * 被调用方节点信息
     */
    private static class CalleeNodeInfo {
        final CallRelation relation;

        CalleeNodeInfo(CallRelation relation) {
            this.relation = relation;
        }

        @Override
        public String toString() {
            return String.format("→ %s%s",
                relation.getCallee().getName(),
                relation.isExternal() ? " [外部]" : "");
        }
    }

    /**
     * 自定义树单元格渲染器
     */
    private static class CallRelationTreeCellRenderer extends javax.swing.tree.DefaultTreeCellRenderer {
        @Override
        public Component getTreeCellRendererComponent(JTree tree, Object value,
                boolean sel, boolean expanded, boolean leaf, int row, boolean hasFocus) {

            super.getTreeCellRendererComponent(tree, value, sel, expanded, leaf, row, hasFocus);

            if (value instanceof DefaultMutableTreeNode) {
                DefaultMutableTreeNode node = (DefaultMutableTreeNode) value;
                Object userObject = node.getUserObject();

                if (userObject instanceof CallerNodeInfo) {
                    // 调用方节点 - 使用蓝色
                    setForeground(sel ? Color.WHITE : new Color(0, 100, 200));
                    setIcon(null);
                } else if (userObject instanceof CalleeNodeInfo) {
                    CalleeNodeInfo info = (CalleeNodeInfo) userObject;
                    // 被调用方节点 - 外部调用用红色，内部调用用绿色
                    if (info.relation.isExternal()) {
                        setForeground(sel ? Color.WHITE : new Color(200, 50, 50));
                    } else {
                        setForeground(sel ? Color.WHITE : new Color(50, 150, 50));
                    }
                    setIcon(null);
                }
            }

            return this;
        }
    }
}