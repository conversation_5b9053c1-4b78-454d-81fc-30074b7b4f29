package com.sankuai.deepcode.astplugin.ui;

import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.CommonDataKeys;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiFile;
import com.intellij.psi.PsiManager;
import com.intellij.ui.JBColor;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBPanel;
import com.intellij.ui.components.JBScrollPane;
import com.intellij.ui.treeStructure.Tree;
import com.sankuai.deepcode.astplugin.DeepCodeASTAnalyzer;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;

import javax.swing.*;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * AST分析主面板
 */
public class ASTAnalysisPanel extends JBPanel<ASTAnalysisPanel> {
    private final Project project;
    private final DeepCodeASTAnalyzer analyzer;
    
    private JBLabel statusLabel;
    private JButton analyzeButton;
    private JButton refreshButton;
    private ASTResultPanel resultPanel;
    
    public ASTAnalysisPanel(Project project) {
        super(new BorderLayout());
        this.project = project;
        this.analyzer = new DeepCodeASTAnalyzer();
        
        initializeUI();
        setupEventHandlers();
    }
    
    private void initializeUI() {
        // 顶部工具栏
        JPanel toolbarPanel = createToolbarPanel();
        add(toolbarPanel, BorderLayout.NORTH);
        
        // 状态标签
        statusLabel = new JBLabel("请选择一个文件进行分析");
        statusLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        add(statusLabel, BorderLayout.SOUTH);
        
        // 结果显示面板
        resultPanel = new ASTResultPanel();
        add(new JBScrollPane(resultPanel), BorderLayout.CENTER);
    }
    
    private JPanel createToolbarPanel() {
        JPanel toolbar = new JBPanel<>(new FlowLayout(FlowLayout.LEFT));
        toolbar.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        
        analyzeButton = new JButton("分析当前文件");
        analyzeButton.setToolTipText("分析当前打开的文件");
        
        refreshButton = new JButton("刷新");
        refreshButton.setToolTipText("刷新分析结果");
        
        toolbar.add(analyzeButton);
        toolbar.add(refreshButton);
        
        return toolbar;
    }
    
    private void setupEventHandlers() {
        analyzeButton.addActionListener(e -> analyzeCurrentFile());
        refreshButton.addActionListener(e -> analyzeCurrentFile());
    }
    
    private void analyzeCurrentFile() {
        VirtualFile currentFile = getCurrentFile();
        if (currentFile == null) {
            statusLabel.setText("没有打开的文件");
            resultPanel.clearResults();
            return;
        }
        
        PsiFile psiFile = PsiManager.getInstance(project).findFile(currentFile);
        if (psiFile == null) {
            statusLabel.setText("无法获取文件的PSI信息");
            resultPanel.clearResults();
            return;
        }
        
        // 在后台线程中执行分析
        SwingUtilities.invokeLater(() -> {
            statusLabel.setText("正在分析: " + currentFile.getName() + "...");
            analyzeButton.setEnabled(false);
            refreshButton.setEnabled(false);
        });
        
        new Thread(() -> {
            try {
                AnalysisResult result = analyzer.analyze(psiFile);
                
                SwingUtilities.invokeLater(() -> {
                    resultPanel.displayResult(result);
                    statusLabel.setText("分析完成: " + currentFile.getName());
                    analyzeButton.setEnabled(true);
                    refreshButton.setEnabled(true);
                });
            } catch (Exception e) {
                SwingUtilities.invokeLater(() -> {
                    statusLabel.setText("分析失败: " + e.getMessage());
                    resultPanel.clearResults();
                    analyzeButton.setEnabled(true);
                    refreshButton.setEnabled(true);
                });
            }
        }).start();
    }
    
    private VirtualFile getCurrentFile() {
        FileEditorManager editorManager = FileEditorManager.getInstance(project);
        VirtualFile[] selectedFiles = editorManager.getSelectedFiles();
        return selectedFiles.length > 0 ? selectedFiles[0] : null;
    }
}