package com.sankuai.deepcode.astplugin.ui;

import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowFactory;
import com.intellij.ui.content.Content;
import com.intellij.ui.content.ContentFactory;

/**
 * AST分析工具窗口工厂类
 */
public class ASTAnalysisToolWindow implements ToolWindowFactory {
    
    @Override
    public void createToolWindowContent(Project project, ToolWindow toolWindow) {
        ASTAnalysisPanel analysisPanel = new ASTAnalysisPanel(project);
        ContentFactory contentFactory = ContentFactory.getInstance();
        Content content = contentFactory.createContent(analysisPanel, "", false);
        toolWindow.getContentManager().addContent(content);
    }
}