package com.sankuai.deepcode.astplugin.ui;

import com.intellij.openapi.actionSystem.ActionUpdateThread;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowManager;


/**
 * 显示AST分析工具窗口的动作
 * <AUTHOR>
 */
public class ShowASTToolWindowAction extends AnAction {
    
    @Override
    public ActionUpdateThread getActionUpdateThread() {
        // 使用BGT以提高性能，避免阻塞EDT
        return ActionUpdateThread.BGT;
    }

    @Override
    public void actionPerformed(AnActionEvent e) {
        Project project = e.getProject();
        if (project == null) {
            return;
        }
        
        try {
            ToolWindowManager toolWindowManager = ToolWindowManager.getInstance(project);
            ToolWindow toolWindow = toolWindowManager.getToolWindow("AST Analysis");

            if (toolWindow != null) {
                // 确保工具窗口可见后再激活
                if (!toolWindow.isVisible()) {
                    toolWindow.show(null);
                } else {
                    toolWindow.activate(null);
                }
            }
        } catch (Exception ex) {
            // 静默处理组件状态异常，避免影响用户体验
            System.err.println("Error activating AST Analysis tool window: " + ex.getMessage());
        }
    }
    
    @Override
    public void update(AnActionEvent e) {
        Project project = e.getProject();
        boolean enabled = project != null;

        try {
            if (enabled) {
                ToolWindowManager toolWindowManager = ToolWindowManager.getInstance(project);
                ToolWindow toolWindow = toolWindowManager.getToolWindow("AST Analysis");
                enabled = toolWindow != null;
            }
        } catch (Exception ex) {
            // 如果获取工具窗口失败，禁用动作
            enabled = false;
        }

        e.getPresentation().setEnabled(enabled);
    }
}