<idea-plugin>
    <id>com.sankuai.deepcode.astplugin</id>
    <name>AST Analysis Plugin</name>
    <version>1.0</version>
    <vendor email="<EMAIL>" url="http://www.deepcode.com">DeepCode</vendor>

    <description><![CDATA[
        A comprehensive plugin to generate structured AST reports using JetBrains PSI API.
        Supports Java, Python, and JavaScript files with detailed analysis of classes, methods, functions, and variables.
        Features include:
        - Detailed AST structure analysis
        - Export functionality for reports
        - Support for multiple programming languages
        - Easy-to-use interface with keyboard shortcuts
        - Structured tool window display
    ]]></description>

    <change-notes><![CDATA[
        Version 1.0:
        - Initial version with basic AST extraction for Java, Python and JavaScript
        - Support for classes, methods, functions, and variables analysis
        - Export functionality for generated reports
        - Keyboard shortcut support (Ctrl+Alt+A)
        - Added structured tool window for better visualization
    ]]></change-notes>

    <idea-version since-build="232" until-build="242.*" />

    <depends>com.intellij.modules.platform</depends>
    <depends>com.intellij.java</depends>

    <extensions defaultExtensionNs="com.intellij">
        <!-- 工具窗口扩展 -->
        <toolWindow id="AST Analysis"
                    factoryClass="com.sankuai.deepcode.astplugin.ui.ASTAnalysisToolWindow"
                    anchor="right"
                    canCloseContents="false"/>
    </extensions>

    <actions>
        <!-- 快速分析Action - 用于开发调试 -->
        <action id="QuickAnalyzer"
                class="com.sankuai.deepcode.astplugin.QuickAnalyzerAction"
                text="Quick AST Analyzer"
                description="Quick analysis for AST analyzer functionality">
            <add-to-group group-id="EditorPopupMenu" anchor="first"/>
            <add-to-group group-id="CodeMenu" anchor="first"/>
            <keyboard-shortcut keymap="$default" first-keystroke="ctrl alt shift T"/>
        </action>

        <action id="ASTReport.ShowToolWindow" class="com.sankuai.deepcode.astplugin.ui.ShowASTToolWindowAction" text="Show AST Analysis" description="Show AST Analysis tool window">
            <add-to-group group-id="MainToolBar" anchor="last" />
            <add-to-group group-id="CodeMenu" anchor="first" />
            <keyboard-shortcut keymap="$default" first-keystroke="ctrl alt T" />
        </action>
        <action id="ASTReport.GenerateReport" class="com.sankuai.deepcode.astplugin.ASTReportAction" text="Generate AST Report" description="Generate structured AST report for the current file">
            <add-to-group group-id="CodeMenu" anchor="last" />
            <keyboard-shortcut keymap="$default" first-keystroke="ctrl alt A" />
        </action>
        <action id="ASTReport.ExportReport" class="com.sankuai.deepcode.astplugin.ExportReportAction" text="Export AST Report" description="Export AST report to file">
            <add-to-group group-id="CodeMenu" anchor="last" />
            <keyboard-shortcut keymap="$default" first-keystroke="ctrl alt E" />
        </action>
    </actions>
</idea-plugin>
