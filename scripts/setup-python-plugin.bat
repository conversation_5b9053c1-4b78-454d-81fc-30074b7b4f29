@echo off
REM 自动设置Python插件的脚本
REM 用于在沙箱IDE中自动安装Python Community Edition插件

set SANDBOX_DIR=build\idea-sandbox-persistent
set CONFIG_DIR=%SANDBOX_DIR%\config
set PLUGINS_DIR=%SANDBOX_DIR%\plugins

echo Setting up Python plugin for sandbox IDE...

REM 创建必要的目录
if not exist "%CONFIG_DIR%\options" mkdir "%CONFIG_DIR%\options"
if not exist "%PLUGINS_DIR%" mkdir "%PLUGINS_DIR%"

REM 创建插件配置文件，预配置Python插件
echo ^<application^> > "%CONFIG_DIR%\options\ide.general.xml"
echo   ^<component name="GeneralSettings"^> >> "%CONFIG_DIR%\options\ide.general.xml"
echo     ^<option name="showTipsOnStartup" value="false" /^> >> "%CONFIG_DIR%\options\ide.general.xml"
echo     ^<option name="reopenLastProject" value="false" /^> >> "%CONFIG_DIR%\options\ide.general.xml"
echo   ^</component^> >> "%CONFIG_DIR%\options\ide.general.xml"
echo ^</application^> >> "%CONFIG_DIR%\options\ide.general.xml"

REM 创建插件管理器配置
echo ^<application^> > "%CONFIG_DIR%\options\pluginAdvertiser.xml"
echo   ^<component name="PluginAdvertiserSettings"^> >> "%CONFIG_DIR%\options\pluginAdvertiser.xml"
echo     ^<option name="suggestionIntervalDays" value="0" /^> >> "%CONFIG_DIR%\options\pluginAdvertiser.xml"
echo     ^<option name="enabledForProject" value="true" /^> >> "%CONFIG_DIR%\options\pluginAdvertiser.xml"
echo   ^</component^> >> "%CONFIG_DIR%\options\pluginAdvertiser.xml"
echo ^</application^> >> "%CONFIG_DIR%\options\pluginAdvertiser.xml"

REM 创建文件类型配置
echo ^<application^> > "%CONFIG_DIR%\options\filetypes.xml"
echo   ^<component name="FileTypeManager" version="18"^> >> "%CONFIG_DIR%\options\filetypes.xml"
echo     ^<extensionMap^> >> "%CONFIG_DIR%\options\filetypes.xml"
echo       ^<mapping ext="py" type="Python" /^> >> "%CONFIG_DIR%\options\filetypes.xml"
echo       ^<mapping ext="pyw" type="Python" /^> >> "%CONFIG_DIR%\options\filetypes.xml"
echo       ^<mapping ext="pyi" type="Python" /^> >> "%CONFIG_DIR%\options\filetypes.xml"
echo     ^</extensionMap^> >> "%CONFIG_DIR%\options\filetypes.xml"
echo   ^</component^> >> "%CONFIG_DIR%\options\filetypes.xml"
echo ^</application^> >> "%CONFIG_DIR%\options\filetypes.xml"

REM 创建启动提示文件
echo Python Plugin Setup Instructions: > "%CONFIG_DIR%\PYTHON_PLUGIN_SETUP.txt"
echo ================================ >> "%CONFIG_DIR%\PYTHON_PLUGIN_SETUP.txt"
echo. >> "%CONFIG_DIR%\PYTHON_PLUGIN_SETUP.txt"
echo If Python plugin is not automatically available, please follow these steps: >> "%CONFIG_DIR%\PYTHON_PLUGIN_SETUP.txt"
echo. >> "%CONFIG_DIR%\PYTHON_PLUGIN_SETUP.txt"
echo 1. Go to File -^> Settings -^> Plugins >> "%CONFIG_DIR%\PYTHON_PLUGIN_SETUP.txt"
echo 2. Search for "Python Community Edition" >> "%CONFIG_DIR%\PYTHON_PLUGIN_SETUP.txt"
echo 3. Install the plugin >> "%CONFIG_DIR%\PYTHON_PLUGIN_SETUP.txt"
echo 4. Restart the IDE >> "%CONFIG_DIR%\PYTHON_PLUGIN_SETUP.txt"
echo. >> "%CONFIG_DIR%\PYTHON_PLUGIN_SETUP.txt"
echo The plugin ID is: PythonCore >> "%CONFIG_DIR%\PYTHON_PLUGIN_SETUP.txt"
echo Plugin URL: https://plugins.jetbrains.com/plugin/7322-python-community-edition >> "%CONFIG_DIR%\PYTHON_PLUGIN_SETUP.txt"
echo. >> "%CONFIG_DIR%\PYTHON_PLUGIN_SETUP.txt"
echo This setup only needs to be done once per sandbox environment. >> "%CONFIG_DIR%\PYTHON_PLUGIN_SETUP.txt"

echo Python plugin setup completed!
echo Sandbox directory: %SANDBOX_DIR%
echo Setup instructions available in: %CONFIG_DIR%\PYTHON_PLUGIN_SETUP.txt

pause
