#!/bin/bash

# 自动设置Python插件的脚本
# 用于在沙箱IDE中自动安装Python Community Edition插件

SANDBOX_DIR="build/idea-sandbox-persistent"
CONFIG_DIR="$SANDBOX_DIR/config"
PLUGINS_DIR="$SANDBOX_DIR/plugins"

echo "Setting up Python plugin for sandbox IDE..."

# 创建必要的目录
mkdir -p "$CONFIG_DIR/options"
mkdir -p "$PLUGINS_DIR"

# 创建插件配置文件，预配置Python插件
cat > "$CONFIG_DIR/options/ide.general.xml" << 'EOF'
<application>
  <component name="GeneralSettings">
    <option name="showTipsOnStartup" value="false" />
    <option name="reopenLastProject" value="false" />
  </component>
</application>
EOF

# 创建插件管理器配置
cat > "$CONFIG_DIR/options/pluginAdvertiser.xml" << 'EOF'
<application>
  <component name="PluginAdvertiserSettings">
    <option name="suggestionIntervalDays" value="0" />
    <option name="enabledForProject" value="true" />
  </component>
</application>
EOF

# 创建项目配置，启用Python文件类型识别
cat > "$CONFIG_DIR/options/filetypes.xml" << 'EOF'
<application>
  <component name="FileTypeManager" version="18">
    <extensionMap>
      <mapping ext="py" type="Python" />
      <mapping ext="pyw" type="Python" />
      <mapping ext="pyi" type="Python" />
    </extensionMap>
  </component>
</application>
EOF

# 创建启动提示文件
cat > "$CONFIG_DIR/PYTHON_PLUGIN_SETUP.txt" << 'EOF'
Python Plugin Setup Instructions:
================================

If Python plugin is not automatically available, please follow these steps:

1. Go to File -> Settings -> Plugins
2. Search for "Python Community Edition" 
3. Install the plugin
4. Restart the IDE

The plugin ID is: PythonCore
Plugin URL: https://plugins.jetbrains.com/plugin/7322-python-community-edition

This setup only needs to be done once per sandbox environment.
EOF

echo "Python plugin setup completed!"
echo "Sandbox directory: $SANDBOX_DIR"
echo "Setup instructions available in: $CONFIG_DIR/PYTHON_PLUGIN_SETUP.txt"

# 使脚本可执行
chmod +x "$0"
