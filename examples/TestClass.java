package examples;

import java.util.List;
import java.util.ArrayList;

/**
 * 示例类，用于测试AST分析插件
 */
public class TestClass {
    private String name;
    private int count;
    private List<String> items;
    
    public TestClass(String name) {
        this.name = name;
        this.count = 0;
        this.items = new ArrayList<>();
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public int getCount() {
        return count;
    }
    
    public void incrementCount() {
        this.count++;
        logOperation("increment");
    }
    
    public void addItem(String item) {
        if (item != null && !item.isEmpty()) {
            items.add(item);
            incrementCount();
        }
    }
    
    public List<String> getItems() {
        return new ArrayList<>(items);
    }
    
    public void processItems() {
        for (String item : items) {
            processItem(item);
        }
    }
    
    private void processItem(String item) {
        System.out.println("Processing: " + item);
        logOperation("process");
    }
    
    private void logOperation(String operation) {
        System.out.println("Operation: " + operation + " on " + name);
    }
    
    public static void main(String[] args) {
        TestClass test = new TestClass("Example");
        test.addItem("Item 1");
        test.addItem("Item 2");
        test.processItems();
        
        System.out.println("Final count: " + test.getCount());
    }
}