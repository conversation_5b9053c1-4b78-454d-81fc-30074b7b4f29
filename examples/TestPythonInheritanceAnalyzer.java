package examples;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectManager;
import com.intellij.psi.PsiFile;
import com.intellij.psi.PsiManager;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.openapi.vfs.LocalFileSystem;
import com.sankuai.deepcode.astplugin.analyzer.PythonASTAnalyzer;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.AnalysisNode;
import com.sankuai.deepcode.astplugin.model.CallRelation;

import java.io.File;
import java.util.Map;

/**
 * 测试增强的Python AST分析器
 * 专门测试__init__方法调用和super()调用的分析功能
 */
public class TestPythonInheritanceAnalyzer {
    
    public static void main(String[] args) {
        System.out.println("=== 测试增强的Python AST分析器 ===");
        System.out.println("测试__init__方法调用和super()调用的分析功能");
        
        try {
            // 获取项目实例
            Project project = ProjectManager.getInstance().getDefaultProject();
            
            // 获取测试Python文件
            String pythonFilePath = "examples/test_python_inheritance.py";
            File pythonFile = new File(pythonFilePath);
            
            if (!pythonFile.exists()) {
                System.err.println("错误：找不到测试文件 " + pythonFilePath);
                System.err.println("请确保文件存在并重新运行测试");
                return;
            }
            
            // 获取虚拟文件
            VirtualFile virtualFile = LocalFileSystem.getInstance().findFileByIoFile(pythonFile);
            if (virtualFile == null) {
                System.err.println("错误：无法获取虚拟文件 " + pythonFilePath);
                return;
            }
            
            // 获取PSI文件
            PsiFile psiFile = PsiManager.getInstance(project).findFile(virtualFile);
            if (psiFile == null) {
                System.err.println("错误：无法获取PSI文件 " + pythonFilePath);
                return;
            }
            
            System.out.println("成功加载Python文件: " + pythonFilePath);
            System.out.println("文件大小: " + virtualFile.getLength() + " 字节");
            System.out.println();
            
            // 在应用程序线程中执行分析
            ApplicationManager.getApplication().runReadAction(() -> {
                try {
                    // 创建Python AST分析器
                    PythonASTAnalyzer analyzer = new PythonASTAnalyzer();
                    
                    // 执行分析
                    System.out.println("开始分析Python文件...");
                    AnalysisResult result = analyzer.analyze(psiFile);
                    
                    // 输出分析结果
                    printAnalysisResults(result);
                    
                } catch (Exception e) {
                    System.err.println("分析过程中发生错误: " + e.getMessage());
                    e.printStackTrace();
                }
            });
            
        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 打印分析结果
     */
    private static void printAnalysisResults(AnalysisResult result) {
        System.out.println("=== 分析结果 ===");
        System.out.println("文件名: " + result.getFileName());
        System.out.println("语言: " + result.getLanguage());
        System.out.println();
        
        // 打印统计信息
        printStatistics(result);
        
        // 打印节点信息
        printNodes(result);
        
        // 打印调用关系
        printCallRelations(result);
        
        // 打印错误信息
        printErrors(result);
    }
    
    /**
     * 打印统计信息
     */
    private static void printStatistics(AnalysisResult result) {
        System.out.println("=== 统计信息 ===");
        Map<String, Integer> statistics = result.getStatistics();
        
        for (Map.Entry<String, Integer> entry : statistics.entrySet()) {
            System.out.println(entry.getKey() + ": " + entry.getValue());
        }
        System.out.println();
    }
    
    /**
     * 打印节点信息
     */
    private static void printNodes(AnalysisResult result) {
        System.out.println("=== 发现的节点 ===");
        Map<String, AnalysisNode> nodes = result.getNodes();
        
        // 按类型分组显示
        System.out.println("类 (Classes):");
        nodes.values().stream()
            .filter(node -> node.getType() == AnalysisNode.NodeType.CLASS)
            .forEach(node -> System.out.println("  - " + node.getSignature() + " (行 " + node.getLineNumber() + ")"));
        
        System.out.println("\n函数/方法 (Functions/Methods):");
        nodes.values().stream()
            .filter(node -> node.getType() == AnalysisNode.NodeType.FUNCTION)
            .forEach(node -> {
                String type = node.getClassName().isEmpty() ? "函数" : "方法";
                System.out.println("  - " + node.getSignature() + " (" + type + ", 行 " + node.getLineNumber() + ")");
            });
        
        System.out.println("\n变量 (Variables):");
        nodes.values().stream()
            .filter(node -> node.getType() == AnalysisNode.NodeType.VARIABLE)
            .forEach(node -> System.out.println("  - " + node.getSignature() + " (行 " + node.getLineNumber() + ")"));
        
        System.out.println();
    }
    
    /**
     * 打印调用关系
     */
    private static void printCallRelations(AnalysisResult result) {
        System.out.println("=== 调用关系 ===");
        
        if (result.getCallRelations().isEmpty()) {
            System.out.println("未发现调用关系");
            System.out.println();
            return;
        }
        
        // 按类型分组显示调用关系
        System.out.println("__init__方法调用:");
        result.getCallRelations().stream()
            .filter(relation -> relation.getCallee().getName().equals("__init__"))
            .forEach(relation -> {
                System.out.println("  " + relation.getCaller().getSignature() + 
                                 " -> " + relation.getCallee().getSignature() + 
                                 " (行 " + relation.getLineNumber() + ")");
                
                // 显示调用实例
                relation.getCallInstances().forEach(instance -> 
                    System.out.println("    调用: " + instance.getExpression() + " (行 " + instance.getLineNumber() + ")"));
            });
        
        System.out.println("\nsuper()方法调用:");
        result.getCallRelations().stream()
            .filter(relation -> relation.getCallee().getSignature().contains("super"))
            .forEach(relation -> {
                System.out.println("  " + relation.getCaller().getSignature() + 
                                 " -> " + relation.getCallee().getSignature() + 
                                 " (行 " + relation.getLineNumber() + ")");
                
                // 显示调用实例
                relation.getCallInstances().forEach(instance -> 
                    System.out.println("    调用: " + instance.getExpression() + " (行 " + instance.getLineNumber() + ")"));
            });
        
        System.out.println("\n父类方法调用:");
        result.getCallRelations().stream()
            .filter(relation -> !relation.getCallee().getName().equals("__init__") && 
                              !relation.getCallee().getSignature().contains("super") &&
                              relation.isExternalCall())
            .forEach(relation -> {
                System.out.println("  " + relation.getCaller().getSignature() + 
                                 " -> " + relation.getCallee().getSignature() + 
                                 " (行 " + relation.getLineNumber() + ")");
                
                // 显示调用实例
                relation.getCallInstances().forEach(instance -> 
                    System.out.println("    调用: " + instance.getExpression() + " (行 " + instance.getLineNumber() + ")"));
            });
        
        System.out.println("\n其他函数调用:");
        result.getCallRelations().stream()
            .filter(relation -> !relation.getCallee().getName().equals("__init__") && 
                              !relation.getCallee().getSignature().contains("super") &&
                              !relation.isExternalCall())
            .forEach(relation -> {
                System.out.println("  " + relation.getCaller().getSignature() + 
                                 " -> " + relation.getCallee().getSignature() + 
                                 " (行 " + relation.getLineNumber() + ")");
                
                // 显示调用实例
                relation.getCallInstances().forEach(instance -> 
                    System.out.println("    调用: " + instance.getExpression() + " (行 " + instance.getLineNumber() + ")"));
            });
        
        System.out.println();
    }
    
    /**
     * 打印错误信息
     */
    private static void printErrors(AnalysisResult result) {
        if (!result.getErrors().isEmpty()) {
            System.out.println("=== 错误信息 ===");
            result.getErrors().forEach(error -> System.out.println("错误: " + error));
            System.out.println();
        }
    }
}