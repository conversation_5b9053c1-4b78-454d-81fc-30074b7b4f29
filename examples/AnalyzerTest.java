package examples;

/**
 * 简单的测试类，用于验证AST分析器的修复效果
 * 
 * 测试要点：
 * 1. 只应该分析当前类中定义的方法，不应该包含外部方法
 * 2. 类定义的行号应该正确
 */
public class AnalyzerTest {
    
    private String name;
    private int value;
    
    // 构造方法
    public AnalyzerTest(String name) {
        this.name = name;
        this.value = 0;
    }
    
    // 内部方法调用
    public void processData() {
        initialize();
        calculate();
        display();
    }
    
    // 私有方法
    private void initialize() {
        this.value = 10;
        System.out.println("Initialized"); // 外部调用，不应该被包含在节点中
    }
    
    private int calculate() {
        int result = this.value * 2;
        return result;
    }
    
    private void display() {
        int result = calculate(); // 内部方法调用
        System.out.println("Result: " + result); // 外部调用
    }
    
    // 外部调用示例
    public void externalCallExample() {
        String text = "test";
        int length = text.length(); // String.length() - 外部调用
        
        java.util.List<String> list = new java.util.ArrayList<>(); // 外部类调用
        list.add("item"); // 外部方法调用
        
        System.out.println("Length: " + length); // 外部调用
    }
    
    // 主方法
    public static void main(String[] args) {
        AnalyzerTest test = new AnalyzerTest("test");
        test.processData();
        test.externalCallExample();
    }
}