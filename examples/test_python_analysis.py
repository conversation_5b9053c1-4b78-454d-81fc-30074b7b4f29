#!/usr/bin/env python3
"""
Python代码分析测试文件
包含各种Python语法结构，用于测试PythonASTAnalyzer的功能
"""

import os
import sys
from typing import List, Dict, Optional

# 模块级变量
MODULE_VERSION = "1.0.0"
DEBUG_MODE = True
config_data = {"host": "localhost", "port": 8080}

def utility_function(data: str) -> str:
    """工具函数"""
    return data.upper()

def validate_input(input_data: str) -> bool:
    """验证输入数据"""
    if not input_data:
        return False
    
    # 调用工具函数
    processed = utility_function(input_data)
    return len(processed) > 0

class DataProcessor:
    """数据处理器类"""
    
    def __init__(self, name: str):
        self.name = name
        self.processed_count = 0
    
    def process_item(self, item: str) -> str:
        """处理单个数据项"""
        # 调用模块级函数
        if not validate_input(item):
            return ""
        
        # 调用工具函数
        result = utility_function(item)
        self.processed_count += 1
        
        # 调用其他方法
        self.log_processing(item, result)
        return result
    
    def log_processing(self, original: str, processed: str) -> None:
        """记录处理过程"""
        if DEBUG_MODE:
            print(f"Processed: {original} -> {processed}")
    
    def get_stats(self) -> Dict[str, int]:
        """获取统计信息"""
        return {"processed_count": self.processed_count}

class AdvancedProcessor(DataProcessor):
    """高级数据处理器"""
    
    def __init__(self, name: str, batch_size: int = 10):
        super().__init__(name)
        self.batch_size = batch_size
    
    def process_batch(self, items: List[str]) -> List[str]:
        """批量处理数据"""
        results = []
        for item in items:
            # 调用父类方法
            processed = self.process_item(item)
            if processed:
                results.append(processed)
        
        # 调用自己的方法
        self.optimize_results(results)
        return results
    
    def optimize_results(self, results: List[str]) -> None:
        """优化结果"""
        # 调用工具函数
        for i, result in enumerate(results):
            results[i] = utility_function(result)

def create_processor(processor_type: str = "basic") -> DataProcessor:
    """工厂函数：创建处理器"""
    if processor_type == "advanced":
        return AdvancedProcessor("advanced_processor")
    else:
        return DataProcessor("basic_processor")

def process_file(file_path: str) -> Optional[List[str]]:
    """处理文件"""
    # 调用验证函数
    if not validate_input(file_path):
        return None
    
    # 创建处理器
    processor = create_processor("advanced")
    
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        # 调用批量处理方法
        if isinstance(processor, AdvancedProcessor):
            results = processor.process_batch(lines)
        else:
            results = [processor.process_item(line) for line in lines]
        
        return results
    except FileNotFoundError:
        print(f"File not found: {file_path}")
        return None

def fibonacci(n: int) -> int:
    """递归函数：计算斐波那契数列"""
    if n <= 1:
        return n
    # 递归调用自身
    return fibonacci(n - 1) + fibonacci(n - 2)

def calculate_fibonacci_sequence(count: int) -> List[int]:
    """计算斐波那契数列"""
    sequence = []
    for i in range(count):
        # 调用递归函数
        fib_value = fibonacci(i)
        sequence.append(fib_value)
    return sequence

def main():
    """主函数"""
    print("Python代码分析测试开始")
    
    # 调用各种函数测试调用关系
    test_data = ["hello", "world", "python"]
    
    # 测试工厂函数
    processor = create_processor("advanced")
    
    # 测试批量处理
    if isinstance(processor, AdvancedProcessor):
        results = processor.process_batch(test_data)
        print(f"处理结果: {results}")
    
    # 测试递归函数
    fib_sequence = calculate_fibonacci_sequence(10)
    print(f"斐波那契数列: {fib_sequence}")
    
    # 测试文件处理
    file_results = process_file("test.txt")
    if file_results:
        print(f"文件处理结果: {len(file_results)} 行")
    
    print("测试完成")

if __name__ == "__main__":
    main()