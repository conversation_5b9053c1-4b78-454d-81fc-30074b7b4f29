#!/usr/bin/env python3
"""
Python继承和super()调用测试文件
用于测试增强的Python AST分析器对__init__方法调用和super()调用的分析能力
"""

class Animal:
    """基础动物类"""
    
    def __init__(self, name, age):
        self.name = name
        self.age = age
        print(f"Animal {name} created")
    
    def speak(self):
        print(f"{self.name} makes a sound")
    
    def move(self):
        print(f"{self.name} is moving")


class Dog(Animal):
    """狗类，继承自Animal"""
    
    def __init__(self, name, age, breed):
        # super().__init__调用 - 现代Python风格
        super().__init__(name, age)
        self.breed = breed
        print(f"Dog {name} of breed {breed} created")
    
    def speak(self):
        super().speak()  # super()方法调用
        print(f"{self.name} barks: Woof!")
    
    def fetch(self):
        print(f"{self.name} is fetching")


class Cat(Animal):
    """猫类，继承自Animal"""
    
    def __init__(self, name, age, color):
        # 直接父类__init__调用 - 旧式Python风格
        Animal.__init__(self, name, age)
        self.color = color
        print(f"Cat {name} with {color} color created")
    
    def speak(self):
        Animal.speak(self)  # 直接父类方法调用
        print(f"{self.name} meows: Meow!")
    
    def climb(self):
        print(f"{self.name} is climbing")


class Robot:
    """机器人类"""
    
    def __init__(self, model, version):
        self.model = model
        self.version = version
        print(f"Robot {model} v{version} initialized")
    
    def operate(self):
        print(f"Robot {self.model} is operating")


class RobotDog(Dog, Robot):
    """机器狗类，多重继承"""
    
    def __init__(self, name, age, breed, model, version):
        # 多重继承中的super()调用
        super(RobotDog, self).__init__(name, age, breed)
        Robot.__init__(self, model, version)
        self.battery_level = 100
        print(f"RobotDog {name} created")
    
    def speak(self):
        super().speak()  # 调用Dog的speak方法
        print(f"Robot {self.name} processes audio: BEEP BEEP")
    
    def charge(self):
        self.battery_level = 100
        print(f"{self.name} is charging")


class PetOwner:
    """宠物主人类 - 组合模式示例"""
    
    def __init__(self, name, pet_type="dog"):
        self.name = name
        
        # 组合模式：在__init__中创建其他类的实例
        if pet_type == "dog":
            self.pet = Dog("Buddy", 3, "Golden Retriever")
        elif pet_type == "cat":
            self.pet = Cat("Whiskers", 2, "orange")
        else:
            self.pet = Animal("Unknown", 1)
        
        # 创建机器人助手
        self.robot_assistant = Robot("Helper", "2.0")
        
        print(f"PetOwner {name} created with {pet_type}")
    
    def interact_with_pet(self):
        self.pet.speak()
        self.pet.move()
    
    def use_robot(self):
        self.robot_assistant.operate()


class AdvancedRobot(Robot):
    """高级机器人类"""
    
    def __init__(self, model, version, ai_level):
        # 使用super(type(self), self)形式
        super(type(self), self).__init__(model, version)
        self.ai_level = ai_level
        print(f"AdvancedRobot with AI level {ai_level} created")
    
    def think(self):
        print(f"AdvancedRobot {self.model} is thinking at level {self.ai_level}")


def create_pets():
    """创建各种宠物的函数"""
    # 创建普通动物
    animal = Animal("Generic", 5)
    
    # 创建狗
    dog = Dog("Rex", 4, "Labrador")
    
    # 创建猫
    cat = Cat("Fluffy", 3, "white")
    
    # 创建机器狗
    robot_dog = RobotDog("Robo", 1, "Mechanical", "X1", "1.0")
    
    # 创建宠物主人
    owner = PetOwner("Alice", "dog")
    
    # 创建高级机器人
    advanced_robot = AdvancedRobot("Einstein", "3.0", 10)
    
    return [animal, dog, cat, robot_dog, owner, advanced_robot]


def test_inheritance():
    """测试继承关系"""
    pets = create_pets()
    
    for pet in pets:
        if hasattr(pet, 'speak'):
            pet.speak()
        if hasattr(pet, 'move'):
            pet.move()


if __name__ == "__main__":
    print("Testing Python inheritance and super() calls...")
    test_inheritance()
    print("Test completed!")