package com.example.test;

import java.util.List;
import java.util.ArrayList;

/**
 * 测试类，用于验证AST分析器功能
 */
public class TestJavaFile {
    private String name;
    private int count;
    
    public TestJavaFile(String name) {
        this.name = name;
        this.count = 0;
    }
    
    public void processData() {
        // 第一次调用helper方法
        helper("start");
        
        for (int i = 0; i < 5; i++) {
            // 在循环中多次调用同一个方法
            helper("processing " + i);
            calculate(i);
        }
        
        // 再次调用helper方法
        helper("end");
        
        // 调用外部方法
        System.out.println("Processing complete");
        List<String> list = new ArrayList<>();
        list.add("test");
    }
    
    private void helper(String message) {
        System.out.println(message);
        count++;
    }
    
    private int calculate(int value) {
        return value * 2;
    }
    
    public String getName() {
        return name;
    }
    
    public int getCount() {
        return count;
    }
}