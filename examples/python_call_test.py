#!/usr/bin/env python3
"""
Python调用关系测试文件

测试场景：
1. 简单函数调用
2. 递归调用
3. 类方法调用
4. 嵌套函数调用
5. 条件调用
"""

def helper_function():
    """辅助函数"""
    print("Helper function called")
    return True

def validate_input(data):
    """验证输入数据"""
    if not data:
        return False
    return helper_function()

def process_data(input_data):
    """处理数据的主函数"""
    # 调用验证函数
    if not validate_input(input_data):
        print("Invalid input")
        return None
    
    # 调用转换函数
    result = transform_data(input_data)
    
    # 调用保存函数
    save_result(result)
    
    return result

def transform_data(data):
    """转换数据"""
    transformed = []
    for item in data:
        # 调用单项转换函数
        transformed_item = transform_item(item)
        transformed.append(transformed_item)
    return transformed

def transform_item(item):
    """转换单个数据项"""
    if isinstance(item, str):
        return item.upper()
    return str(item)

def save_result(result):
    """保存结果"""
    print(f"Saving result: {result}")
    # 调用写文件函数
    write_to_file(result)

def write_to_file(data):
    """写入文件"""
    print(f"Writing to file: {data}")

# 递归函数示例
def fibonacci(n):
    """计算斐波那契数列"""
    if n <= 1:
        return n
    # 递归调用自己
    return fibonacci(n - 1) + fibonacci(n - 2)

def factorial(n):
    """计算阶乘"""
    if n <= 1:
        return 1
    # 递归调用
    return n * factorial(n - 1)

# 类定义
class DataProcessor:
    """数据处理器类"""
    
    def __init__(self, name):
        self.name = name
        self.data = []
    
    def add_item(self, item):
        """添加数据项"""
        self.data.append(item)
        # 调用日志函数
        log_message(f"Added item: {item}")
    
    def process_all(self):
        """处理所有数据"""
        results = []
        for item in self.data:
            # 调用处理单项方法
            processed = self.process_single_item(item)
            results.append(processed)
        
        # 调用验证方法
        if self.validate_results(results):
            return results
        return None
    
    def process_single_item(self, item):
        """处理单个数据项"""
        # 调用全局函数
        return transform_item(item)
    
    def validate_results(self, results):
        """验证结果"""
        # 调用全局验证函数
        return validate_input(results)

def log_message(message):
    """记录日志消息"""
    print(f"[LOG] {message}")

def main():
    """主函数"""
    # 测试简单函数调用
    test_data = ["hello", "world", 123]
    result = process_data(test_data)
    
    # 测试递归函数
    fib_result = fibonacci(5)
    fact_result = factorial(4)
    
    # 测试类方法调用
    processor = DataProcessor("test_processor")
    processor.add_item("test1")
    processor.add_item("test2")
    processed_results = processor.process_all()
    
    # 调用日志函数
    log_message("Main function completed")

# 嵌套函数示例
def outer_function():
    """外层函数"""
    
    def inner_function():
        """内层函数"""
        # 调用全局函数
        helper_function()
        return "inner result"
    
    def another_inner():
        """另一个内层函数"""
        # 调用同级内层函数
        result = inner_function()
        return f"another: {result}"
    
    # 调用内层函数
    inner_result = inner_function()
    another_result = another_inner()
    
    # 调用全局函数
    log_message(f"Outer function results: {inner_result}, {another_result}")
    
    return inner_result, another_result

# 条件调用示例
def conditional_calls(condition):
    """条件调用示例"""
    if condition:
        # 条件调用
        result = process_data(["conditional", "data"])
    else:
        # 另一个条件调用
        result = transform_data(["other", "data"])
    
    # 无条件调用
    log_message("Conditional processing completed")
    return result

if __name__ == "__main__":
    main()
    
    # 测试其他函数
    outer_function()
    conditional_calls(True)
    conditional_calls(False)