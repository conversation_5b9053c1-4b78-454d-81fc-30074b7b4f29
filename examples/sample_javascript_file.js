/**
 * Sample JavaScript file for testing AST Analysis Plugin
 */

// Global variables
const APP_NAME = "AST Demo";
let currentUser = null;
var debugMode = false;

// Class definition
class Person {
    constructor(name, age) {
        this.name = name;
        this.age = age;
        this.hobbies = [];
    }
    
    addHobby(hobby) {
        if (hobby && hobby.trim()) {
            this.hobbies.push(hobby);
        }
    }
    
    removeHobby(hobby) {
        const index = this.hobbies.indexOf(hobby);
        if (index > -1) {
            this.hobbies.splice(index, 1);
            return true;
        }
        return false;
    }
    
    getInfo() {
        return {
            name: this.name,
            age: this.age,
            hobbies: [...this.hobbies]
        };
    }
    
    toString() {
        return `Person(name: ${this.name}, age: ${this.age}, hobbies: [${this.hobbies.join(', ')}])`;
    }
}

// Function declarations
function createPerson(name, age) {
    return new Person(name, age);
}

function validateAge(age) {
    return typeof age === 'number' && age >= 0 && age <= 150;
}

function validateName(name) {
    return typeof name === 'string' && name.trim().length > 0;
}

// Arrow functions
const formatPersonInfo = (person) => {
    return `${person.name} (${person.age} years old)`;
};

const filterAdults = (people) => {
    return people.filter(person => person.age >= 18);
};

const sortByAge = (people) => {
    return people.sort((a, b) => a.age - b.age);
};

// Async function
async function fetchUserData(userId) {
    try {
        // Simulated API call
        const response = await fetch(`/api/users/${userId}`);
        const userData = await response.json();
        return userData;
    } catch (error) {
        console.error('Error fetching user data:', error);
        return null;
    }
}

// Higher-order function
function createPersonProcessor(processingFunction) {
    return function(people) {
        return people.map(processingFunction);
    };
}

// Object with methods
const PersonManager = {
    people: [],
    
    addPerson(person) {
        if (person instanceof Person) {
            this.people.push(person);
        }
    },
    
    removePerson(name) {
        const index = this.people.findIndex(p => p.name === name);
        if (index > -1) {
            this.people.splice(index, 1);
            return true;
        }
        return false;
    },
    
    findPerson(name) {
        return this.people.find(p => p.name === name);
    },
    
    getAllPeople() {
        return [...this.people];
    },
    
    getAdults() {
        return filterAdults(this.people);
    }
};

// Main execution function
function main() {
    console.log(`Starting ${APP_NAME}`);
    
    // Create sample data
    const person1 = createPerson("Alice", 25);
    person1.addHobby("reading");
    person1.addHobby("swimming");
    
    const person2 = createPerson("Bob", 17);
    person2.addHobby("gaming");
    
    // Add to manager
    PersonManager.addPerson(person1);
    PersonManager.addPerson(person2);
    
    // Process data
    const allPeople = PersonManager.getAllPeople();
    const adults = PersonManager.getAdults();
    const sortedPeople = sortByAge(allPeople);
    
    console.log("All people:", allPeople.map(formatPersonInfo));
    console.log("Adults:", adults.map(formatPersonInfo));
    console.log("Sorted by age:", sortedPeople.map(formatPersonInfo));
}

// Event handler function
function handleUserLogin(event) {
    const username = event.target.value;
    if (validateName(username)) {
        currentUser = username;
        console.log(`User logged in: ${currentUser}`);
    }
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        Person,
        PersonManager,
        createPerson,
        validateAge,
        validateName,
        formatPersonInfo,
        filterAdults,
        sortByAge
    };
}

// Run main function if this is the main module
if (typeof window !== 'undefined') {
    // Browser environment
    document.addEventListener('DOMContentLoaded', main);
} else {
    // Node.js environment
    main();
}
