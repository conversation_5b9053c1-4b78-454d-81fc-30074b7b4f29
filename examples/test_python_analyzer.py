#!/usr/bin/env python3
"""
Python AST分析器测试文件

测试要点：
1. 类定义和方法定义
2. 函数定义和调用关系
3. 变量定义
4. 模块级别的函数调用
"""

import os
import sys
from typing import List, Dict

# 模块级变量
MODULE_NAME = "test_python_analyzer"
VERSION = "1.0.0"
DEBUG_MODE = True

class DataProcessor:
    """数据处理器类"""
    
    def __init__(self, name: str):
        self.name = name
        self.data = []
        self.processed = False
    
    def add_data(self, item):
        """添加数据项"""
        self.data.append(item)
        log_message(f"Added item: {item}")
    
    def process_data(self):
        """处理数据"""
        if not self.data:
            log_error("No data to process")
            return False
        
        for item in self.data:
            processed_item = transform_item(item)
            self.data[self.data.index(item)] = processed_item
        
        self.processed = True
        log_message("Data processing completed")
        return True
    
    def get_results(self):
        """获取处理结果"""
        if not self.processed:
            log_warning("Data not processed yet")
            return []
        return self.data

class FileHandler:
    """文件处理器类"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
    
    def read_file(self):
        """读取文件"""
        try:
            with open(self.file_path, 'r') as f:
                content = f.read()
            log_message(f"File read successfully: {self.file_path}")
            return content
        except Exception as e:
            log_error(f"Failed to read file: {e}")
            return None
    
    def write_file(self, content: str):
        """写入文件"""
        try:
            with open(self.file_path, 'w') as f:
                f.write(content)
            log_message(f"File written successfully: {self.file_path}")
            return True
        except Exception as e:
            log_error(f"Failed to write file: {e}")
            return False

# 模块级函数
def log_message(message: str):
    """记录信息日志"""
    if DEBUG_MODE:
        print(f"[INFO] {message}")

def log_warning(message: str):
    """记录警告日志"""
    print(f"[WARNING] {message}")

def log_error(message: str):
    """记录错误日志"""
    print(f"[ERROR] {message}")

def transform_item(item):
    """转换数据项"""
    if isinstance(item, str):
        return item.upper().strip()
    elif isinstance(item, (int, float)):
        return item * 2
    else:
        return str(item)

def validate_data(data: List):
    """验证数据"""
    if not data:
        log_warning("Empty data provided")
        return False
    
    for item in data:
        if item is None:
            log_error("Found None item in data")
            return False
    
    log_message("Data validation passed")
    return True

def process_file(file_path: str, processor_name: str = "default"):
    """处理文件的主函数"""
    log_message(f"Starting file processing: {file_path}")
    
    # 创建文件处理器
    file_handler = FileHandler(file_path)
    content = file_handler.read_file()
    
    if content is None:
        log_error("Failed to read file content")
        return False
    
    # 创建数据处理器
    processor = DataProcessor(processor_name)
    
    # 模拟数据处理
    lines = content.split('\n')
    for line in lines:
        if line.strip():
            processor.add_data(line.strip())
    
    # 验证和处理数据
    if validate_data(processor.data):
        success = processor.process_data()
        if success:
            results = processor.get_results()
            log_message(f"Processing completed. Results count: {len(results)}")
            return True
    
    log_error("File processing failed")
    return False

def main():
    """主函数"""
    log_message("Starting Python analyzer test")
    
    # 测试文件处理
    test_file = "/tmp/test_data.txt"
    success = process_file(test_file, "test_processor")
    
    if success:
        log_message("Test completed successfully")
    else:
        log_error("Test failed")

# 递归函数示例
def fibonacci(n: int) -> int:
    """计算斐波那契数列"""
    if n <= 1:
        return n
    return fibonacci(n - 1) + fibonacci(n - 2)

def factorial(n: int) -> int:
    """计算阶乘"""
    if n <= 1:
        return 1
    return n * factorial(n - 1)

if __name__ == "__main__":
    main()
    
    # 测试递归函数
    fib_result = fibonacci(10)
    fact_result = factorial(5)
    
    log_message(f"Fibonacci(10): {fib_result}")
    log_message(f"Factorial(5): {fact_result}")