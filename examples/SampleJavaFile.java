package com.example.demo;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;

/**
 * Sample Java class for testing AST Analysis Plugin
 */
public class SampleJavaFile extends Object implements Runnable {
    
    // Fields
    private String name;
    private int age;
    private List<String> hobbies;
    private static final String CONSTANT = "DEMO";
    
    // Constructor
    public SampleJavaFile(String name, int age) {
        this.name = name;
        this.age = age;
        this.hobbies = new ArrayList<>();
    }
    
    // Getter methods
    public String getName() {
        return name;
    }
    
    public int getAge() {
        return age;
    }
    
    public List<String> getHobbies() {
        return hobbies;
    }
    
    // Setter methods
    public void setName(String name) {
        this.name = name;
    }
    
    public void setAge(int age) {
        this.age = age;
    }
    
    // Business methods
    public void addHobby(String hobby) {
        if (hobby != null && !hobby.trim().isEmpty()) {
            hobbies.add(hobby);
        }
    }
    
    public void removeHobby(String hobby) {
        hobbies.remove(hobby);
    }
    
    public Map<String, Object> toMap() {
        Map<String, Object> result = new HashMap<>();
        result.put("name", name);
        result.put("age", age);
        result.put("hobbies", hobbies);
        return result;
    }
    
    @Override
    public void run() {
        System.out.println("Running: " + name);
        processHobbies();
    }
    
    private void processHobbies() {
        for (String hobby : hobbies) {
            System.out.println("Hobby: " + hobby);
        }
    }
    
    // Static method
    public static String getConstant() {
        return CONSTANT;
    }
    
    @Override
    public String toString() {
        return String.format("SampleJavaFile{name='%s', age=%d, hobbies=%s}", 
                           name, age, hobbies);
    }
}
