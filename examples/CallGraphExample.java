package examples;

import java.util.List;
import java.util.ArrayList;

/**
 * 函数调用关系分析示例
 * 包含各种调用模式：递归调用、链式调用、外部调用等
 */
public class CallGraphExample {
    
    private List<String> data = new ArrayList<>();
    
    // 入口方法
    public void processData() {
        initializeData();
        validateData();
        transformData();
        saveResults();
    }
    
    // 初始化数据
    private void initializeData() {
        data.add("item1");
        data.add("item2");
        data.add("item3");
        logMessage("Data initialized");
    }
    
    // 验证数据
    private boolean validateData() {
        if (data.isEmpty()) {
            logError("Data is empty");
            return false;
        }
        
        for (String item : data) {
            if (!isValidItem(item)) {
                logError("Invalid item: " + item);
                return false;
            }
        }
        
        logMessage("Data validation passed");
        return true;
    }
    
    // 验证单个项目
    private boolean isValidItem(String item) {
        return item != null && !item.trim().isEmpty();
    }
    
    // 转换数据
    private void transformData() {
        List<String> transformedData = new ArrayList<>();
        
        for (String item : data) {
            String transformed = transformItem(item);
            transformedData.add(transformed);
        }
        
        data = transformedData;
        logMessage("Data transformation completed");
    }
    
    // 转换单个项目
    private String transformItem(String item) {
        return item.toUpperCase().trim();
    }
    
    // 保存结果
    private void saveResults() {
        if (validateData()) {
            writeToFile();
            logMessage("Results saved successfully");
        } else {
            logError("Cannot save invalid data");
        }
    }
    
    // 写入文件
    private void writeToFile() {
        // 模拟文件写入操作
        System.out.println("Writing data to file...");
        for (String item : data) {
            System.out.println("  " + item);
        }
    }
    
    // 日志记录方法
    private void logMessage(String message) {
        System.out.println("[INFO] " + getCurrentTimestamp() + ": " + message);
    }
    
    private void logError(String error) {
        System.err.println("[ERROR] " + getCurrentTimestamp() + ": " + error);
    }
    
    // 获取当前时间戳
    private String getCurrentTimestamp() {
        return String.valueOf(System.currentTimeMillis());
    }
    
    // 递归方法示例
    public int fibonacci(int n) {
        if (n <= 1) {
            return n;
        }
        return fibonacci(n - 1) + fibonacci(n - 2);
    }
    
    // 计算阶乘（另一个递归示例）
    public long factorial(long n) {
        if (n <= 1) {
            return 1;
        }
        return n * factorial(n - 1);
    }
    
    // 链式调用示例
    public CallGraphExample configure() {
        return this.setData("config1")
                  .setData("config2")
                  .setData("config3");
    }
    
    private CallGraphExample setData(String value) {
        data.add(value);
        return this;
    }
    
    // 外部调用示例
    public void demonstrateExternalCalls() {
        // 调用Java标准库方法
        String text = "Hello World";
        int length = text.length();
        String upper = text.toUpperCase();
        
        // 调用ArrayList方法
        List<String> list = new ArrayList<>();
        list.add("test");
        list.size();
        list.clear();
        
        // 调用System方法
        System.out.println("Length: " + length);
        System.currentTimeMillis();
    }
    
    // 主方法
    public static void main(String[] args) {
        CallGraphExample example = new CallGraphExample();
        
        // 调用主要处理流程
        example.processData();
        
        // 调用递归方法
        int fib = example.fibonacci(10);
        long fact = example.factorial(5);
        
        // 调用链式配置
        example.configure();
        
        // 演示外部调用
        example.demonstrateExternalCalls();
        
        System.out.println("Fibonacci(10): " + fib);
        System.out.println("Factorial(5): " + fact);
    }
}