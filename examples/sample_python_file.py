"""
Sample Python file for testing AST Analysis Plugin
"""

import os
import sys
from typing import List, Dict, Optional


class Person:
    """A sample Person class"""
    
    def __init__(self, name: str, age: int):
        self.name = name
        self.age = age
        self.hobbies = []
    
    def add_hobby(self, hobby: str) -> None:
        """Add a hobby to the person"""
        if hobby and hobby.strip():
            self.hobbies.append(hobby)
    
    def remove_hobby(self, hobby: str) -> bool:
        """Remove a hobby from the person"""
        if hobby in self.hobbies:
            self.hobbies.remove(hobby)
            return True
        return False
    
    def get_info(self) -> Dict[str, any]:
        """Get person information as dictionary"""
        return {
            'name': self.name,
            'age': self.age,
            'hobbies': self.hobbies
        }
    
    def __str__(self) -> str:
        return f"Person(name='{self.name}', age={self.age}, hobbies={self.hobbies})"


class Student(Person):
    """A student class that extends Person"""
    
    def __init__(self, name: str, age: int, student_id: str):
        super().__init__(name, age)
        self.student_id = student_id
        self.grades = {}
    
    def add_grade(self, subject: str, grade: float) -> None:
        """Add a grade for a subject"""
        self.grades[subject] = grade
    
    def get_average_grade(self) -> Optional[float]:
        """Calculate average grade"""
        if not self.grades:
            return None
        return sum(self.grades.values()) / len(self.grades)
    
    def __str__(self) -> str:
        return f"Student(id='{self.student_id}', name='{self.name}', age={self.age})"


# Top-level functions
def create_person(name: str, age: int) -> Person:
    """Factory function to create a Person"""
    return Person(name, age)


def create_student(name: str, age: int, student_id: str) -> Student:
    """Factory function to create a Student"""
    return Student(name, age, student_id)


def process_people(people: List[Person]) -> None:
    """Process a list of people"""
    for person in people:
        print(person)
        if isinstance(person, Student):
            avg_grade = person.get_average_grade()
            if avg_grade:
                print(f"Average grade: {avg_grade:.2f}")


def main():
    """Main function"""
    # Create some sample data
    person1 = create_person("Alice", 25)
    person1.add_hobby("reading")
    person1.add_hobby("swimming")
    
    student1 = create_student("Bob", 20, "S12345")
    student1.add_hobby("coding")
    student1.add_grade("Math", 85.5)
    student1.add_grade("Physics", 92.0)
    
    people = [person1, student1]
    process_people(people)


if __name__ == "__main__":
    main()
