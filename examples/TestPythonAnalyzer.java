package examples;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectManager;
import com.intellij.psi.PsiFile;
import com.intellij.psi.PsiManager;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.openapi.vfs.LocalFileSystem;
import com.sankuai.deepcode.astplugin.analyzer.PythonASTAnalyzer;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.AnalysisNode;
import com.sankuai.deepcode.astplugin.model.CallRelation;

import java.io.File;

/**
 * Python AST分析器测试程序
 *
 * 用于验证PythonASTAnalyzer的功能：
 * - 类定义解析
 * - 函数定义解析
 * - 变量定义解析
 * - 函数调用关系分析
 *
 * <AUTHOR>
 */
public class TestPythonAnalyzer {
    
    public static void main(String[] args) {
        System.setProperty("ast.analyzer.debug", "true");
        
        ApplicationManager.getApplication().runReadAction(() -> {
            try {
                // 获取项目
                Project[] projects = ProjectManager.getInstance().getOpenProjects();
                if (projects.length == 0) {
                    System.err.println("No open projects found");
                    return;
                }

                Project project = projects[0];

                // 查找测试Python文件
                String pythonFilePath = "examples/test_python_analysis.py";
                File file = new File(pythonFilePath);

                if (!file.exists()) {
                    System.err.println("Python test file not found: " + pythonFilePath);
                    return;
                }

                VirtualFile virtualFile = LocalFileSystem.getInstance().findFileByIoFile(file);
                if (virtualFile == null) {
                    System.err.println("Could not find virtual file for: " + pythonFilePath);
                    return;
                }

                PsiFile psiFile = PsiManager.getInstance(project).findFile(virtualFile);
                if (psiFile == null) {
                    System.err.println("Could not create PSI file for: " + pythonFilePath);
                    return;
                }

                System.out.println("=== Python AST分析器测试开始 ===");
                System.out.println("分析文件: " + pythonFilePath);
                System.out.println();

                // 创建分析器并分析文件
                PythonASTAnalyzer analyzer = new PythonASTAnalyzer();
                AnalysisResult result = analyzer.analyze(psiFile);

                // 输出分析结果
                printAnalysisResults(result);

                System.out.println("=== 测试完成 ===");

            } catch (Exception e) {
                System.err.println("测试过程中发生错误: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    /**
     * 打印分析结果
     */
    private static void printAnalysisResults(AnalysisResult result) {
        System.out.println("=== 分析结果统计 ===");
        System.out.println("文件名: " + result.getFileName());
        System.out.println("语言: " + result.getLanguage());
        System.out.println("总节点数: " + result.getNodes().size());
        System.out.println("调用关系数: " + result.getCallRelations().size());

        // 打印统计信息
        if (!result.getStatistics().isEmpty()) {
            System.out.println("\n统计信息:");
            result.getStatistics().forEach((key, value) ->
                System.out.println("  " + key + ": " + value));
        }

        // 打印错误信息
        if (!result.getErrors().isEmpty()) {
            System.out.println("\n错误信息:");
            result.getErrors().forEach(error ->
                System.out.println("  ERROR: " + error));
        }

        System.out.println();

        // 按类型分组显示节点
        printNodesByType(result);

        // 显示调用关系
        printCallRelations(result);
    }
    
    /**
     * 按类型打印节点
     */
    private static void printNodesByType(AnalysisResult result) {
        System.out.println("=== 节点详情 ===");

        // 分类统计
        long classCount = result.getNodes().values().stream()
            .filter(node -> node.getType() == AnalysisNode.NodeType.CLASS)
            .count();
        long functionCount = result.getNodes().values().stream()
            .filter(node -> node.getType() == AnalysisNode.NodeType.FUNCTION)
            .count();
        long variableCount = result.getNodes().values().stream()
            .filter(node -> node.getType() == AnalysisNode.NodeType.VARIABLE)
            .count();

        System.out.println("类: " + classCount + " 个");
        System.out.println("函数: " + functionCount + " 个");
        System.out.println("变量: " + variableCount + " 个");
        System.out.println();

        // 打印类定义
        System.out.println("--- 类定义 ---");
        result.getNodes().values().stream()
            .filter(node -> node.getType() == AnalysisNode.NodeType.CLASS)
            .forEach(node -> System.out.printf("  %s (行 %d): %s\n",
                node.getName(), node.getLineNumber(), node.getSignature()));

        // 打印函数定义
        System.out.println("\n--- 函数定义 ---");
        result.getNodes().values().stream()
            .filter(node -> node.getType() == AnalysisNode.NodeType.FUNCTION)
            .forEach(node -> System.out.printf("  %s (行 %d): %s\n",
                node.getName(), node.getLineNumber(), node.getSignature()));

        // 打印变量定义
        System.out.println("\n--- 变量定义 ---");
        result.getNodes().values().stream()
            .filter(node -> node.getType() == AnalysisNode.NodeType.VARIABLE)
            .forEach(node -> System.out.printf("  %s (行 %d): %s\n",
                node.getName(), node.getLineNumber(), node.getSignature()));

        System.out.println();
    }
    
    /**
     * 打印调用关系
     */
    private static void printCallRelations(AnalysisResult result) {
        System.out.println("=== 函数调用关系 ===");

        if (result.getCallRelations().isEmpty()) {
            System.out.println("未发现函数调用关系");
            return;
        }
        
        System.out.println("发现 " + result.getCallRelations().size() + " 个调用关系:");

        for (CallRelation relation : result.getCallRelations()) {
            System.out.printf("\n%s -> %s\n",
                relation.getCaller().getName(),
                relation.getCallee().getName());

            System.out.printf("  调用者: %s (行 %d)\n",
                relation.getCaller().getSignature(),
                relation.getCaller().getLineNumber());

            System.out.printf("  被调用者: %s (行 %d)\n",
                relation.getCallee().getSignature(),
                relation.getCallee().getLineNumber());

            System.out.printf("  调用位置: 行 %d\n",
                relation.getCallLineNumber());

            System.out.printf("  调用表达式: %s\n",
                relation.getCallExpression());

            // 显示所有调用实例
            if (relation.getAllCallInstances().size() > 1) {
                System.out.println("  所有调用实例:");
                for (CallRelation.CallInstance instance : relation.getAllCallInstances()) {
                    System.out.printf("    行 %d: %s\n",
                        instance.getLineNumber(),
                        instance.getExpression());
                }
            }
        }
        
        System.out.println();
    }
}