package com.example.another;

import com.example.test.TestJavaFile;
import java.util.List;
import java.util.ArrayList;

/**
 * 多包示例，用于验证内外部调用判定
 * 这个类在不同的包中，但仍然是同一个工程
 */
public class MultiPackageExample {
    
    public void demonstrateCallTypes() {
        // 内部调用 - 同一工程内不同包的类
        TestJavaFile testFile = new TestJavaFile("demo");
        testFile.processData();  // 应该被识别为内部调用
        
        // 外部调用 - Java标准库
        System.out.println("This is external call");  // 外部调用
        
        // 外部调用 - 集合框架
        List<String> list = new ArrayList<>();  // 外部调用
        list.add("item");  // 外部调用
        
        // 内部调用 - 同一类内的方法
        helperMethod();  // 内部调用
    }
    
    private void helperMethod() {
        System.out.println("Helper method in same class");
    }
}