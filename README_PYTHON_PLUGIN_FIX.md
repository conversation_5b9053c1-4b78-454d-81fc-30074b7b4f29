# Python 插件沙箱环境自动配置解决方案

## 🎯 问题解决

**原问题**：每次启动沙箱IDE都需要重新安装Python Community Edition插件

**解决方案**：自动化配置脚本 + 持久化沙箱环境

## 🚀 快速开始

### 1. 一键设置
```bash
# 设置Python插件环境
./gradlew setupPythonPlugin

# 启动IDE（会自动运行设置）
./gradlew runIde
```

### 2. 首次使用Python插件
当IDE启动后，如果Python插件未自动可用：

1. 打开 `File` → `Settings` → `Plugins`
2. 搜索 "Python Community Edition"
3. 点击 `Install` 安装
4. 重启IDE

**重要**：这个安装过程只需要做一次！

## 🔧 技术实现

### 持久化沙箱配置
```kotlin
// build.gradle.kts
intellij {
    sandboxDir.set("${project.layout.buildDirectory.get()}/idea-sandbox-persistent")
}
```

### 自动化设置脚本
- **Linux/macOS**: `scripts/setup-python-plugin.sh`
- **Windows**: `scripts/setup-python-plugin.bat`

### 预配置文件
脚本会自动创建：
```
build/idea-sandbox-persistent/
├── config/
│   ├── options/
│   │   ├── ide.general.xml          # IDE基础配置
│   │   ├── pluginAdvertiser.xml     # 插件管理器配置
│   │   └── filetypes.xml           # Python文件类型识别
│   └── PYTHON_PLUGIN_SETUP.txt     # 安装说明
└── plugins/                         # 插件目录
```

## 📋 使用流程

### 开发流程
```bash
# 1. 构建项目
./gradlew build

# 2. 设置Python环境（首次或清理后）
./gradlew setupPythonPlugin

# 3. 启动IDE
./gradlew runIde

# 4. 测试Python分析功能
# - 打开 test_python_sample.py
# - 使用 Ctrl+Alt+Shift+T 分析
# - 查看 AST Analysis 工具窗口
```

### 验证功能
打开测试文件后，应该能看到：
- ✅ 递归调用检测：`recursive_fibonacci -> recursive_fibonacci (recursive)`
- ✅ 字符串方法：`string_operations_demo -> str.upper`
- ✅ 列表方法：`collection_operations_demo -> list.append`
- ✅ 字典方法：`collection_operations_demo -> dict.update`
- ✅ Super调用：`AdvancedProcessor.__init__ -> super.__init__`

## 🛠️ 故障排除

### 问题1：脚本权限错误
```bash
# 解决方案
chmod +x scripts/setup-python-plugin.sh
```

### 问题2：Python插件未出现
```bash
# 手动安装步骤
# 1. File -> Settings -> Plugins
# 2. Marketplace -> 搜索 "Python Community Edition"
# 3. Install -> Restart IDE
```

### 问题3：沙箱环境损坏
```bash
# 清理并重新设置
rm -rf build/idea-sandbox-persistent
./gradlew setupPythonPlugin runIde
```

## 📊 改进效果

### 修复前
- ❌ 每次启动都需要重新安装Python插件
- ❌ 开发体验不一致
- ❌ 浪费时间在环境配置上

### 修复后
- ✅ 一次配置，永久使用
- ✅ 自动化设置脚本
- ✅ 持久化沙箱环境
- ✅ 开发体验一致

## 🎯 核心特性

### 1. 自动化配置
- 自动创建沙箱目录结构
- 预配置Python文件类型识别
- 优化插件管理器设置

### 2. 持久化环境
- 沙箱配置在多次启动间保持
- 插件安装状态持久化
- 避免重复配置

### 3. 跨平台支持
- Linux/macOS: Bash脚本
- Windows: Batch脚本
- Gradle任务统一接口

### 4. 开发友好
- 详细的设置说明
- 故障排除指南
- 一键启动命令

## 📝 技术细节

### Gradle任务集成
```kotlin
// 自动运行设置脚本
register("setupPythonPlugin", Exec::class) {
    if (System.getProperty("os.name").toLowerCase().contains("windows")) {
        commandLine("cmd", "/c", "scripts\\setup-python-plugin.bat")
    } else {
        commandLine("bash", "scripts/setup-python-plugin.sh")
    }
}

// runIde依赖设置任务
runIde {
    dependsOn("setupSandbox", "setupPythonPlugin")
}
```

### 配置文件模板
脚本会生成标准的IntelliJ配置文件，确保：
- Python文件类型正确识别
- 插件管理器优化配置
- IDE启动体验优化

## 🎉 总结

这个解决方案彻底解决了沙箱IDE中Python插件重复安装的问题：

1. **自动化**：一键设置，无需手动配置
2. **持久化**：配置永久保存，避免重复工作
3. **智能化**：跨平台支持，自动检测环境
4. **用户友好**：详细说明，故障排除指南

现在开发者可以专注于插件功能开发，而不用担心环境配置问题！
